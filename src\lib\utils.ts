import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export const formatTime = (timeString: string) => {
  if (!timeString) return '';
  
  try {
    // Parse the time string "11/06/2025 16:00:00" and format to "11/06/2025 16:00"
    const timeMatch = timeString.match(/^(.+?)\s+(\d{1,2}):(\d{2}):\d{2}$/);
    if (timeMatch) {
      const datePart = timeMatch[1]; // "11/06/2025"
      const hours = timeMatch[2];     // "16"
      const minutes = timeMatch[3];   // "00"
      return `${datePart} ${hours}:${minutes}`;
    }
    
    // Fallback: use a stable UTC-based formatting to avoid SSR/client locale/timezone mismatch
    const date = new Date(timeString);
    if (!isNaN(date.getTime())) {
      const day = String(date.getUTCDate()).padStart(2, '0');
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const year = date.getUTCFullYear();
      const hours = String(date.getUTCHours()).padStart(2, '0');
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      return `${day}/${month}/${year} ${hours}:${minutes}`;
    }
    
    return timeString;
  } catch (error) {
    console.warn('Error formatting time:', timeString, error);
    return timeString;
  }
};


export const getStatusText = (status: string) => {
  switch (status.toLowerCase()) {
    case 'live':
      return 'Trực tiếp';
    case 'pending':
      return 'Chưa diễn ra';
    case 'upcoming':
      return 'Sắp tới';
    case 'finished':
      return 'Đã kết thúc';
    case 'ht':
    case 'halftime':
      return 'HT';
    default:
      return status;
  }
};