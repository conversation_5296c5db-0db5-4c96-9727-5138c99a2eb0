"use client";

import { useEffect, useState, useMemo, useRef } from "react";
import { useRouter, useParams } from "next/navigation";
import { useLeagues, useLeagueSearch } from "@/hooks/useLeagues";
import { ChevronDown } from "lucide-react";
import { Search } from "lucide-react";
import { League, LeagueGroup } from "@/types/league.types"; // type League đã định nghĩa
import { motion, AnimatePresence } from "framer-motion";

function LeagueLogo({
  leagueId,
  name,
  className,
}: {
  leagueId: string | number;
  name: string;
  className?: string;
}) {
  return (
    <>
      <img
        src={`https://images.fotmob.com/image_resources/logo/leaguelogo/${leagueId}.png`}
        alt={name}
        className={`block dark:hidden object-contain rounded ${className}`}
      />
      <img
        src={`https://images.fotmob.com/image_resources/logo/leaguelogo/dark/${leagueId}.png`}
        alt={name}
        className={`hidden dark:block object-contain rounded ${className}`}
      />
    </>
  );
}
export function LeagueSearch({ onSelect }: { onSelect: (leagueId: string) => void }) {
  const [query, setQuery] = useState("");
  const [open, setOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 🧠 Đóng khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setOpen(false);
        setQuery("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Tìm kiếm giải đấu
  const { results, loading } = useLeagueSearch(query);

  // Chỉ gọi API 1 lần ở đây
  const { countries, international } = useLeagues();

  return (
    <div>
      {/* Mobile search (ẩn trên desktop) */}
      <div ref={containerRef} className="relative w-full">
        {/* Ô search */}
        <div className="flex items-center gap-2 px-3 py-2 border rounded-lg bg-white dark:bg-gray-900 dark:border-gray-700 ">
          <Search className="w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Tìm giải đấu..."
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setOpen(true);
            }}
            onFocus={() => setOpen(true)}
            className="flex-1 bg-transparent focus:outline-none text-gray-900 dark:text-white"
          />
        </div>

        {/* Dropdown kết quả */}
        {open && query.trim() && (
          <div
            className="
              absolute left-0 right-0 
              mt-1 
              bg-white dark:bg-gray-900 
              border border-gray-200 dark:border-gray-700 
              rounded-lg shadow-lg 
              max-h-80 overflow-auto 
              z-50
              sm:hidden
            "
          >
            {loading ? (
              <div className="px-4 py-2 text-sm text-gray-400">Đang tải...</div>
            ) : results.length > 0 ? (
              results.map((l) => (
                <button
                  key={l.id}
                  onClick={() => {
                    onSelect(String(l.id));
                    setQuery("");
                    setOpen(false);
                  }}
                  className="
                    w-full px-4 py-2 text-left text-sm 
                    hover:bg-blue-100 dark:hover:bg-blue-800 
                    flex items-start gap-3
                  "
                >
                  <LeagueLogo
                    leagueId={l.id}
                    name={l.name}
                    className="w-6 h-6 rounded-full flex-shrink-0 mt-0.5"
                  />
                  <span className="text-gray-900 dark:text-gray-100 break-words whitespace-normal leading-snug">
                    {l.name}
                  </span>
                </button>
              ))
            ) : (
              <div className="px-4 py-2 text-sm text-gray-400">Không tìm thấy</div>
            )}
          </div>
        )}
      </div>

      {/* Desktop group list (ẩn trên mobile) */}
      <div className="hidden sm:block">
        <LeagueGroupList
          countries={countries}
          international={international}
          onSelect={onSelect}
          filter={query}
        />
      </div>
    </div>
  );
}

// LeagueGroupList.tsx
type LeagueGroupListProps = {
  countries: LeagueGroup[];
  international: LeagueGroup[];
  filter?: string;
  onSelect: (leagueId: string) => void;
};

export function LeagueGroupList({ countries, international, filter = "", onSelect }: LeagueGroupListProps) {
  const [openGroups, setOpenGroups] = useState<Record<string, boolean>>({});

  const toggleGroup = (ccode: string) => {
    setOpenGroups((prev) => ({ ...prev, [ccode]: !prev[ccode] }));
  };

  const flagUrl = (ccode: string) => {
    if (ccode === "INT") return `https://images.fotmob.com/image_resources/logo/teamlogo/${ccode.toLowerCase()}.png`; 
    return `https://images.fotmob.com/image_resources/logo/teamlogo/${ccode.toLowerCase()}.png`;
  };

  // check toàn cục
  const lowerFilter = filter.toLowerCase();
  const allGroups = [...international, ...countries];
  const hasAnyMatch = lowerFilter
    ? allGroups.some(g =>
        g.leagues.some(l => l.localizedName.toLowerCase().includes(lowerFilter))
      )
    : true;

  const renderGroup = (group: LeagueGroup) => {
    const hasMatch = group.leagues.some((l) =>
      l.localizedName.toLowerCase().includes(lowerFilter)
    );

    // nếu đang filter mà không có match toàn cục => render nguyên trạng
    const effectiveFilter = hasAnyMatch ? lowerFilter : "";

    const sortedLeagues = group.leagues
      .filter((l) =>
        !effectiveFilter || l.localizedName.toLowerCase().includes(effectiveFilter)
      )
      .sort((a, b) => a.localizedName.localeCompare(b.localizedName));

    if (effectiveFilter && sortedLeagues.length === 0) return null;

    const isOpen = effectiveFilter
      ? hasMatch
      : !!openGroups[group.ccode];

    return (
      <div key={group.ccode} className="border rounded-lg">
        <div
          className="flex items-center justify-between cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={() => {
            if (!effectiveFilter) toggleGroup(group.ccode);
          }}
        >
          <div className="flex items-center">
            <img
              src={flagUrl(group.ccode)}
              alt={group.localizedName}
              className="w-4 h-4 rounded mr-4"
            />
            <span>{group.localizedName}</span>
          </div>
          <ChevronDown
            className={`transition-transform duration-200 ${isOpen ? "rotate-180" : "rotate-0"}`}
          />
        </div>

        <AnimatePresence initial={false}>
          {isOpen && (
            <motion.ul
              key="content"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.1, ease: "easeInOut" }}
              className="pl-8 pr-4 py-2 space-y-1 overflow-hidden"
            >
              {sortedLeagues.map((league) => (
                <li
                  key={league.id}
                  className="flex items-center cursor-pointer hover:text-blue-600"
                  onClick={() => onSelect(String(league.id))}
                >
                  <LeagueLogo
                    leagueId={league.id}
                    name={league.localizedName}
                    className="w-4 h-4 mr-4"
                  />
                  <span>{league.localizedName}</span>
                </li>
              ))}
            </motion.ul>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <div>
      {international.map(renderGroup)}
      {countries.map(renderGroup)}
    </div>
  );
}

export default function BxhVaLichThiDauLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const params = useParams();
  const [isNavigating, setIsNavigating] = useState(false);
  const activeLeagueId = params?.leagueId ? String(params.leagueId) : null;

  const {
    popularLeagues,
    countries,
    international,
    selectedLeagueId,
    setSelectedLeagueId,
  } = useLeagues();

  const [open, setOpen] = useState(false);
  const [year, setYear] = useState<number | null>(null);

  // ✅ Thêm state này
  const [hasClickedLeague, setHasClickedLeague] = useState(false);

  useEffect(() => {
    setYear(new Date().getFullYear());
  }, []);

  const leagueIds = useMemo(
    () => [
      ...popularLeagues.map((l) => String(l.id)),
      ...countries.flatMap((g) => g.leagues.map((l) => String(l.id))),
      ...international.flatMap((g) => g.leagues.map((l) => String(l.id))),
    ],
    [popularLeagues, countries, international]
  );

  useEffect(() => {
    if (leagueIds.length === 0) return;

    // Nếu có leagueId trong URL → auto chọn nhưng KHÔNG coi là “click”
    if (activeLeagueId && activeLeagueId !== selectedLeagueId) {
      setSelectedLeagueId(activeLeagueId);
      return;
    }

    // Nếu chưa có selectedLeagueId → chọn mặc định (vẫn chưa click)
    if (!selectedLeagueId) {
      const firstFeatured = popularLeagues.find((l) =>
        ["47", "42"].includes(String(l.id))
      );
      const firstLeague =
        firstFeatured ||
        popularLeagues[0] ||
        countries[0]?.leagues[0] ||
        international[0]?.leagues[0];
      if (firstLeague) setSelectedLeagueId(String(firstLeague.id));
    }
  }, [
    leagueIds,
    selectedLeagueId,
    activeLeagueId,
    setSelectedLeagueId,
    popularLeagues,
    countries,
    international,
  ]);

  const selectedLeague = useMemo(() => {
    return (
      popularLeagues.find((l) => String(l.id) === selectedLeagueId) ||
      countries
        .flatMap((g) => g.leagues)
        .find((l) => String(l.id) === selectedLeagueId) ||
      international
        .flatMap((g) => g.leagues)
        .find((l) => String(l.id) === selectedLeagueId) ||
      null
    );
  }, [popularLeagues, countries, international, selectedLeagueId]);

  const nextYear = year ? year + 1 : null;

  useEffect(() => {
    if (activeLeagueId) {
      setHasClickedLeague(true);
    } else {
      setHasClickedLeague(false);
    }
  }, [activeLeagueId]);

  const handleSelectLeague = (leagueId: string) => {
    router.push(`/bxh-va-lich-thi-dau/${leagueId}/lich-thi-dau`);
  };


  if (!year) return null;

  return (
    <div className="flex flex-col md:flex-row md:gap-6 lg:gap-8">
      {/* Sidebar */}
      <aside className="md:w-[220px] lg:w-[260px] xl:w-[280px] flex flex-col gap-6 px-2 sm:px-0">
        {/* Giải đấu nổi bật */}
        <div className="hidden md:block bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-4 mt-6">
          <h2 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
            Giải đấu nổi bật
          </h2>
          <div className="flex flex-col space-y-2">
            {popularLeagues.map((l) => {
              const active = String(l.id) === selectedLeagueId;
              return (
                <button
                  key={l.id}
                  onClick={() => handleSelectLeague(String(l.id))}
                  className={`w-full px-3 py-2 text-sm rounded-lg flex items-center gap-3 transition-all duration-200 ${
                    active
                      ? "bg-blue-600 text-white shadow-md scale-[1.02] cursor-default"
                      : "text-gray-700 dark:text-gray-300 hover:bg-blue-200 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-400"
                  }`}
                >
                  <LeagueLogo
                    leagueId={l.id}
                    name={l.name}
                    className="w-7 h-7"
                  />
                  <span className="truncate text-left flex-1">{l.name}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Tìm giải đấu */}
        <div className="w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-4 mt-6 md:mt-0 mb-6">
          <LeagueSearch onSelect={handleSelectLeague} />
        </div>
      </aside>
      {/* 🔽 Mobile Dropdown - Hiển thị danh sách giải đấu */}
      <div className="block md:hidden mb-4 relative mx-2">
        <label className="text-xl font-bold mb-2 flex justify-center text-gray-900 dark:text-white">
          Giải đấu nổi bật
        </label>

        {/* Nút hiện giải đang chọn */}
        <div
          className="flex items-center justify-between w-full px-4 py-3 rounded-xl border-2 border-blue-500 
                      bg-white dark:bg-gray-900 shadow font-semibold text-gray-900 dark:text-white cursor-pointer select-none"
          onClick={() => setOpen(!open)}
        >
          {selectedLeague ? (
            <div className="flex items-center gap-2">
              <LeagueLogo
                leagueId={selectedLeague.id}
                name={selectedLeague.name}
                className="w-6 h-6 rounded-full"
              />
              <span className="truncate">{selectedLeague.name}</span>
            </div>
          ) : (
            <span className="text-gray-400">Chọn giải đấu...</span>
          )}
          <ChevronDown
            className={`w-5 h-5 transition-transform ${open ? "rotate-180" : ""}`}
          />
        </div>

        {/* Dropdown menu */}
        {open && (
          <div className="absolute z-50 mt-2 w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-auto">
            {[...popularLeagues, ...countries.flatMap(c => c.leagues), ...international.flatMap(c => c.leagues)]
              .map((l) => {
                const active = String(l.id) === selectedLeagueId;
                return (
                  <button
                    key={l.id}
                    onClick={() => {
                      setOpen(false);
                      handleSelectLeague(String(l.id));
                    }}
                    className={`w-full text-left px-4 py-2 flex items-center gap-3 transition-all duration-200 ${
                      active
                        ? "bg-blue-600 text-white"
                        : "text-gray-700 dark:text-gray-300 hover:bg-blue-200 dark:hover:bg-blue-800 hover:text-blue-700 dark:hover:text-blue-400"
                    }`}
                  >
                    <LeagueLogo leagueId={l.id} name={l.name} className="w-6 h-6" />
                    <span className="truncate flex-1">{l.name}</span>
                  </button>
                );
              })}
          </div>
        )}
      </div>

      {/* Main Content */}
      <main className="flex-1 px-2 sm:px-4 md:px-0">
        {!isNavigating && hasClickedLeague && selectedLeague && (
          <div>
            <div className=" mb-6 mt-6">
              <button
                onClick={() => router.push("/bxh-va-lich-thi-dau")}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                ← Quay lại
              </button>
            </div>

            <section className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 rounded-lg mb-4">
              <div className="flex items-center gap-4 px-4 py-4 sm:px-6 sm:py-6 md:px-8 md:py-8 shadow">
                <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-20 md:h-20">
                  <LeagueLogo
                    leagueId={selectedLeague.id}
                    name={selectedLeague.name}
                    className="w-full h-full"
                  />
                </div>
                <h1 className="text-base sm:text-lg md:text-2xl font-bold text-gray-900 dark:text-white leading-snug">
                  Lịch thi đấu {selectedLeague.name} {year} - {nextYear}
                </h1>
              </div>
            </section>

          </div>
        )}
        {/* Content */}
        <div>{children}</div>
      </main>
    </div>
  );
}

