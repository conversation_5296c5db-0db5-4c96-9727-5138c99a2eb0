"use client";

import { useState, useRef, useEffect } from "react";
import { ChatMessage as ChatMessageType } from "@/services/chatService";
import ChatForm from "../ChatForm";

interface ChatInputProps {
  onSendMessage: (message: string, replyTo?: ChatMessageType) => void;
  onSendAnonymousMessage?: (message: string, senderDisplayName: string) => void;
  isLoggedIn: boolean;
  replyTo?: ChatMessageType | null;
  onCancelReply?: () => void;
  placeholder?: string;
  disabled?: boolean;
  noBorderRadius?: boolean;
  onOpenAuthModal?: (mode: "login" | "register") => void;
}

export default function ChatInput({
  onSendMessage,
  onSendAnonymousMessage,
  isLoggedIn,
  replyTo,
  onCancelReply,
  placeholder = "Nhập tin nhắn...",
  disabled = false,
  noBorderRadius = false,
  onOpenAuthModal,
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [_showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [_isInputFocused, setIsInputFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (replyTo && inputRef.current) {
      inputRef.current.focus();
    }
  }, [replyTo]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || disabled) return;

    const trimmedMessage = message.trim();
    setMessage("");
    onSendMessage(trimmedMessage, replyTo || undefined);
    setShowEmojiPicker(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // const insertEmoji = (emoji: string) => {
  //   setMessage((prev) => prev + emoji);
  //   setShowEmojiPicker(false);
  //   if (inputRef.current) {
  //     inputRef.current.focus();
  //   }
  // };

  const handleFocus = () => {
    setIsInputFocused(true);
  };

  const handleBlur = () => {
    setIsInputFocused(false);
  };

  const handleInputClick = () => {
    if (!isLoggedIn) {
      onOpenAuthModal?.("login");
    }
  };

  if (!isLoggedIn) {
    return (
      <ChatForm
        onLoginClick={handleInputClick}
        onSubmit={(msg, displayName) => {
          if (!msg.trim() || disabled) return;
          const trimmedMessage = msg.trim();
          onSendAnonymousMessage?.(trimmedMessage, displayName);
        }}
        noBorderRadius={noBorderRadius}
      />
    );
  }

  return (
    <div
      className={`border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-700 p-3 z-30 ${
        noBorderRadius ? "rounded-none" : "rounded-b-[10px]"
      }`}
    >
      {/* Reply Preview */}
      {replyTo && (
        <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mb-1">
                Trả lời {replyTo.userName}
              </div>
              <div className="text-sm text-blue-800 dark:text-blue-200 truncate">
                {replyTo.message}
              </div>
            </div>
            <button
              title="cancel-reply"
              onClick={onCancelReply}
              className="ml-2 p-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex items-center space-x-2">
          {/* Emoji Button */}
          {/* <button
            type="button"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
            title="Biểu tượng cảm xúc"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button> */}

          {/* Message Input */}
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              className="w-full rounded-lg border border-gray-200 dark:border-gray-600 pr-12 pl-4 py-2 text-sm outline-none focus:border-blue-400 focus:ring-2 focus:ring-blue-100 dark:focus:ring-blue-900 transition-all duration-200 bg-white dark:bg-custom-dark text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              placeholder={
                isLoggedIn ? placeholder : "Đăng nhập để bình luận..."
              }
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onClick={handleInputClick}
              disabled={disabled}
              inputMode="text"
              enterKeyHint="send"
              autoComplete="off"
              spellCheck="false"
              readOnly={!isLoggedIn}
            />

            {/* Send Button */}
            <button
              title="send-message"
              type="submit"
              disabled={(!message.trim() && isLoggedIn) || disabled}
              className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full bg-transparent p-1.5 text-blue-600 hover:text-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={!isLoggedIn ? handleInputClick : undefined}
            >
              <svg
                className="h-4 w-4 rotate-90"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Emoji Picker */}
        {/* {showEmojiPicker && (
          <div className="absolute bottom-full left-0 mb-2 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-64 overflow-y-auto">
            <div className="grid grid-cols-8 gap-1">
              {emojis.map((emoji, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => insertEmoji(emoji)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors text-lg"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        )} */}
      </form>
    </div>
  );
}
