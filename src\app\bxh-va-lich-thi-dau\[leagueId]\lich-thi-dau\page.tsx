"use client";
import React from "react";
import Link from "next/link";
import { useSearchParams, useParams } from "next/navigation";
import { useLeagues, useLeagueMatch } from "@/hooks";

function parseUtcToLocal(utcString: string) {
  const dateObj = new Date(utcString);

  const formatterDate = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });

  const formatterTime = new Intl.DateTimeFormat("en-GB", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  const date = formatterDate.format(dateObj);
  const time = formatterTime.format(dateObj).toUpperCase();

  return {
    date,
    time,
    timestamp: dateObj.getTime(), // thêm để sắp xếp
  };
}

// Lấy range ngày
function getDaysRange(center = new Date()) {
  const days: { date: Date; label: string; sub: string }[] = [];
  const weekdays = [
    "Chủ nhật",
    "Thứ hai",
    "Th<PERSON> ba",
    "Th<PERSON> tư",
    "Th<PERSON> năm",
    "Th<PERSON> sáu",
    "Th<PERSON> bảy",
  ];
  for (let i = -1; i <= 5; i++) {
    const dt = new Date(center);
    dt.setDate(center.getDate() + i);
    const label = String(dt.getDate());

    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    let sub = weekdays[dt.getDay()];
    if (dt.toDateString() === today.toDateString()) sub = "Hôm nay";
    else if (dt.toDateString() === tomorrow.toDateString()) sub = "Ngày mai";

    days.push({ date: dt, label, sub });
  }
  return days;
}

export default function FixturesPage() {
  // Cho phép bật/tắt feature
  const isRelease = true;

  // Lấy query param ?ngay=
  const searchParams = useSearchParams();
  const ngayParam = searchParams.get("ngay");
  let selectedDate: Date;
  if (!ngayParam) selectedDate = new Date();
  else {
    const [d, m, y] = ngayParam.split("-").map((v) => parseInt(v, 10));
    selectedDate = new Date(y, m - 1, d);
  }

  // Lấy leagueId từ URL
  const params = useParams();
  const leagueIdFromUrl = params?.leagueId as string | undefined;

  // Hooks lấy leagueId từ store
  const { selectedLeagueId } = useLeagues();

  // Quyết định leagueId sẽ dùng
  const effectiveLeagueId = leagueIdFromUrl ?? selectedLeagueId;

  // Lấy matches
  const { matches, loading, error } = useLeagueMatch(effectiveLeagueId);

  if (!isRelease) {
    return (
      <div className="min-h-screen bg-white dark:bg-custom-dark flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Tính năng đang được phát triển
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Chúng tôi đang hoàn thiện tính năng này. Vui lòng quay lại sau!
          </p>
        </div>
      </div>
    );
  }

  const sortedMatches = [...matches].sort((a, b) => {
    const aTime = parseUtcToLocal(a.match.data.status.utcTime).timestamp;
    const bTime = parseUtcToLocal(b.match.data.status.utcTime).timestamp;
    return aTime - bTime; // tăng dần
  });

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Main Content */}
      <main className="flex-1">
        {/* Tabs */}
        <div className="flex gap-2 mb-4 rounded-2xl p-2">
          {effectiveLeagueId ? (
            <>
              <Link
                href={`/bxh-va-lich-thi-dau/${effectiveLeagueId}/lich-thi-dau`}
                className="flex-1 rounded-xl px-4 py-3 text-center text-base font-semibold transition-colors bg-gradient-to-r from-blue-600 to-blue-400 dark:from-blue-700 dark:to-blue-500 text-white"
                style={{ minWidth: 180 }}
              >
                Trận
              </Link>

              <Link
                href={`/bxh-va-lich-thi-dau/${effectiveLeagueId}/bxh`}
                className="flex-1 rounded-xl px-4 py-3 text-center text-base font-semibold transition-colors bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                style={{ minWidth: 180 }}
              >
                Bảng xếp hạng
              </Link>
            </>
          ) : (
            <div className="flex-1 text-center text-gray-500">
              Đang tải mùa giải...
            </div>
          )}
        </div>

        {/* Match Table */}
        <section className="bg-white dark:bg-gray-900 shadow rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
            <span className="border-l-4 border-blue-700 pl-2 mr-2">
              Lịch thi đấu
            </span>
          </h2>

          <div className="overflow-x-auto ">
            <table className="w-full text-sm table-auto">
              <thead>
                <tr className="bg-gray-50 dark:bg-gray-800">
                  <th className="px-3 py-2 text-left font-semibold text-gray-700 dark:text-gray-300 min-w-[112px]">
                    Thời gian
                  </th>
                  <th className="px-3 py-2 text-center font-semibold text-gray-700 dark:text-gray-300 min-w-[100px]">
                    Chủ
                  </th>
                  <th className="px-3 py-2 text-center font-semibold text-gray-700 dark:text-gray-300 min-w-[48px] shrink-0"></th>
                  <th className="px-3 py-2 text-center font-semibold text-gray-700 dark:text-gray-300 min-w-[72px]">
                    Tỷ số
                  </th>
                  <th className="px-3 py-2 text-center font-semibold text-gray-700 dark:text-gray-300 min-w-[48px] shrink-0"></th>
                  <th className="px-3 py-2 text-center font-semibold text-gray-700 dark:text-gray-300 min-w-[100px]">
                    Khách
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td
                      colSpan={6}
                      className="text-center py-4 text-gray-500 dark:text-gray-400"
                    >
                      Đang tải...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={6} className="text-center py-4 text-red-500">
                      Có lỗi khi tải dữ liệu.
                    </td>
                  </tr>
                ) : matches.length === 0 ? (
                  <tr>
                    <td
                      colSpan={6}
                      className="text-center py-4 text-gray-500 dark:text-gray-400"
                    >
                      Không có dữ liệu
                    </td>
                  </tr>
                ) : (
                  [...matches]
                    .sort(
                      (a, b) =>
                        new Date(a.match.data.status.utcTime).getTime() -
                        new Date(b.match.data.status.utcTime).getTime()
                    )
                    .map((m, i) => {
                      const local = parseUtcToLocal(
                        m.match.data.status.utcTime
                      ); // chỉ gọi 1 lần
                      const now = new Date();
                      const matchTime = new Date(m.match.data.status.utcTime);
                      const ninetyMinutesLater = new Date(
                        matchTime.getTime() + 90 * 60 * 1000
                      );

                      // Trạng thái trận
                      let matchStatus;
                      if (now >= matchTime && now <= ninetyMinutesLater) {
                        matchStatus = (
                          <span className="px-2 py-1 text-white bg-red-500 rounded">
                            LIVE
                          </span>
                        );
                      } else if (
                        matchTime >= now &&
                        matchTime <=
                          new Date(now.getTime() + 12 * 60 * 60 * 1000)
                      ) {
                        matchStatus = (
                          <span className="px-2 py-1 text-black bg-yellow-300 rounded">
                            SẮP
                          </span>
                        );
                      } else if (now > ninetyMinutesLater) {
                        matchStatus = (
                          <span className="text-gray-900 dark:text-white">
                            {m.match.data.home.score} -{" "}
                            {m.match.data.away.score}
                          </span>
                        );
                      } else {
                        matchStatus = <span>VS</span>;
                      }

                      return (
                        <tr
                          key={m.match.match_id}
                          className={
                            i % 2 === 0
                              ? "bg-white dark:bg-gray-900"
                              : "bg-gray-50 dark:bg-gray-800"
                          }
                        >
                          {/* Thời gian */}
                          <td className="px-3 py-2 whitespace-pre text-gray-700 dark:text-gray-300">
                            {local.date}
                            <br />
                            {local.time}
                          </td>

                          {/* Tên đội chủ */}
                          <td className="px-3 py-2 text-gray-900 text-center dark:text-white font-medium">
                            {m.match.data.home.name}
                          </td>

                          {/* Logo đội chủ */}
                          <td className="px-3 py-2 align-middle">
                            <div className="flex items-center justify-center w-full h-full">
                              <img
                                src={`https://images.fotmob.com/image_resources/logo/teamlogo/${m.match.data.home.id}_xsmall.png`}
                                alt={m.match.data.home.name}
                                className="w-8 h-8 object-contain shrink-0"
                              />
                            </div>
                          </td>

                          {/* Tỷ số / trạng thái trận */}
                          <td className="px-3 py-2 text-center font-bold text-gray-900 dark:text-white">
                            {matchStatus}
                          </td>

                          {/* Logo đội khách */}
                          <td className="px-3 py-2 align-middle">
                            <div className="flex items-center justify-center w-full h-full">
                              <img
                                src={`https://images.fotmob.com/image_resources/logo/teamlogo/${m.match.data.away.id}_xsmall.png`}
                                alt={m.match.data.away.name}
                                className="w-8 h-8 object-contain shrink-0"
                              />
                            </div>
                          </td>

                          {/* Tên đội khách */}
                          <td className="px-3 py-2 text-gray-900 text-center dark:text-white font-medium">
                            {m.match.data.away.name}
                          </td>
                        </tr>
                      );
                    })
                )}
              </tbody>
            </table>
          </div>
        </section>
      </main>
    </div>
  );
}
