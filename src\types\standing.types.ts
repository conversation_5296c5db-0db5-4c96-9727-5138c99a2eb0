export interface Standing {
  deduction: null;
  draws: number;
  goalConDiff: number;
  id: number;
  idx: number;
  losses: number;
  name: string;
  ongoing: null;
  pageUrl: string;
  played: number;
  pts: number;
  qualColor: string;
  scoresStr: string;
  shortName: string;
  wins: number;
}

export interface RankingTable {
  ccode: string;
  leagueId: number;
  leagueName: string;
  table: {
    all: Standing[];
    away: Standing[];
    home: Standing[];
    xg: Standing[];
  };
}

interface RankingData {
  ccode: string;
  composite: boolean;
  leagueId: number;
  leagueName: "Champions League";
  tables?: RankingTable[];
  table?: RankingTable["table"];
}

export interface RankingResponse {
  data: RankingData;
}
