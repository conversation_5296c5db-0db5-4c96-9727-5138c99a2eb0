"use client";

import { TAB_INDICES, TabIndex } from "@/constants/tabs";
import { useAuth } from "@/contexts/AuthContext";
import { MatchData } from "@/types/match";
import { useState, useRef, useEffect } from "react";
import LineupTab from "../LineupTab";
import StatsTab from "../StatsTab";
import DirectChatRoom from "./DirectChatRoom";
import GeneralChatRoom from "./GeneralChatRoom";
import RelatedMatchesTab from "../RelatedMatchesTab";
import BannerOdds from "../BannerOdds";
import { useBanner } from "@/hooks/useBanner";
import { BannerData } from "@/types/banner";
import AuthModal from "../../AuthModal";

interface ChatTabProps {
  isLoggedIn: boolean;
  matchId?: string;
  chatType?: "general" | "direct";
  selectedRoomId?: string | null;
  activeTab: number;
  matchData?: MatchData | null;
  systemMessage?: {
    id: string;
    content: string;
    created_at: string;
    user_id: string;
    user: {
      full_name: string;
      avatar_url: string;
    };
  } | null;
  onOpenAuthModal: (mode: "login" | "register") => void;
  onTabChange: (tabIndex: TabIndex, roomId?: string) => void;
}

export default function ChatTab({
  isLoggedIn,
  matchData,
  activeTab,
  matchId,
  onOpenAuthModal,
  onTabChange,
}: ChatTabProps) {
  const { user, chatRooms, loadMessagesForRoom, sendMessage, createChat } =
    useAuth();

  const [selectedDirectRoomId, setSelectedDirectRoomId] = useState<
    string | null
  >(null);
  const bannerTriggeredRef = useRef(false);
  const [openChatFromBanner, setOpenChatFromBanner] = useState(false);

  // Banner state and auth modal for banner flow
  const {
    bannerData,
    openAuthModal,
    closeAuthModal,
    isAuthModalOpen,
    authMode,
    setAuthMode,
    hideBanner,
  } = useBanner();
  const pendingBannerRef = useRef<BannerData | null>(null);
  const bannerAuthPendingRef = useRef(false);

  // Handle room change for direct chat
  const handleRoomChange = (roomId: string) => {
    setSelectedDirectRoomId(roomId);
  };

  const handleTabChange = (tabIndex: TabIndex, roomId?: string) => {
    onTabChange(tabIndex);
    if (tabIndex === TabIndex.PRIVATE_CHAT) {
      if (roomId) {
        // Click từ banner: có roomId → mở sub-tab chat
        setSelectedDirectRoomId(roomId);
        bannerTriggeredRef.current = true;
        setOpenChatFromBanner(true);
      } else {
        // Click từ icon: không có roomId → giữ sub-tab betting
        setSelectedDirectRoomId(null);
        bannerTriggeredRef.current = false;
        setOpenChatFromBanner(false);
      }
    }
  };

  const onBannerClick = async () => {
    if (!user) {
      if (bannerData) {
        pendingBannerRef.current = bannerData as BannerData;
      }
      bannerAuthPendingRef.current = true;
      openAuthModal("login");
      return;
    }

    if (!bannerData) return;
    try {
      const chatName = `Chat với ${bannerData.expert.name}`;
      const chatType = "direct" as const;

      const { room, error } = await createChat?.(chatName, chatType);
      if (error) {
        console.error("Error creating chat:", error);
        return;
      }

      if (room) {
        hideBanner();
        handleTabChange(TabIndex.PRIVATE_CHAT, room.id);
      }
    } catch (error) {
      console.error("Error handling banner click:", error);
    }
  };

  // Khi chuyển sang PRIVATE_CHAT từ icon (không có roomId), đảm bảo không mở chat
  useEffect(() => {
    if (activeTab === TabIndex.PRIVATE_CHAT) {
      if (!bannerTriggeredRef.current) {
        setSelectedDirectRoomId(null);
        setOpenChatFromBanner(false);
      }
    }
  }, [activeTab]);

  const roomId =
    chatRooms.find((room) => room.type === "general")?.id ??
    "00000000-0000-0000-0000-000000000001";

  return (
    <div className="h-full flex flex-col relative">
      {activeTab !== TabIndex.PRIVATE_CHAT && (
        <BannerOdds
          currentBanner={bannerData as unknown as BannerData}
          onBannerClick={onBannerClick}
        />
      )}
      {activeTab === TabIndex.PRIVATE_CHAT ? (
        (() => {
          const directRooms =
            chatRooms?.filter((room) => room.type === "direct") || [];
          return (
            <DirectChatRoom
              user={user}
              selectedDirectRoomId={selectedDirectRoomId}
              openChatFromBanner={openChatFromBanner}
              sendMessage={sendMessage}
              loadMessagesForRoom={loadMessagesForRoom}
              directChatRooms={directRooms}
              isLoggedIn={isLoggedIn}
              onOpenAuthModal={onOpenAuthModal}
              onRoomChange={handleRoomChange}
              onCreateChat={createChat}
            />
          );
        })()
      ) : activeTab === TAB_INDICES.STATS ? (
        <div className="h-full flex flex-col">
          <div className="flex-1 min-h-0 overflow-y-auto">
            <StatsTab isLoading={!matchData} matchData={matchData} />
          </div>
        </div>
      ) : activeTab === TAB_INDICES.LINEUP ? (
        <div className="h-full flex flex-col">
          <div className="flex-1 min-h-0 overflow-y-auto">
            <LineupTab isLoading={!matchData} />
          </div>
        </div>
      ) : activeTab === TAB_INDICES.RELATED_MATCHES ? (
        <div className="h-full flex">
          <div className="flex-1 min-h-0 overflow-y-auto">
            <RelatedMatchesTab
              currentMatchId={matchId}
              currentCategory={matchData?.category || "football"}
            />
          </div>
        </div>
      ) : (
        <GeneralChatRoom
          roomId={roomId}
          isLoggedIn={isLoggedIn}
          onOpenAuthModal={onOpenAuthModal}
          onCreateChat={createChat}
          onTabChange={handleTabChange}
        />
      )}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        initialMode={authMode}
        onModeChange={setAuthMode}
        onLoginSuccess={async () => {
          try {
            if (bannerAuthPendingRef.current) {
              const data =
                pendingBannerRef.current ?? (bannerData as BannerData | null);
              if (!data) {
                return;
              }
              const chatName = `Chat với ${data.expert.name}`;
              const { room, error } = await createChat?.(chatName, "direct");
              if (!error && room) {
                hideBanner();
                handleTabChange(TabIndex.PRIVATE_CHAT, room.id);
              }
            }
          } catch (e) {
            console.error("onLoginSuccess banner flow error:", e);
          } finally {
            bannerAuthPendingRef.current = false;
            pendingBannerRef.current = null;
            closeAuthModal();
          }
        }}
      />
    </div>
  );
}
