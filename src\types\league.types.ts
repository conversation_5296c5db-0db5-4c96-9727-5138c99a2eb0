// =============================
// League (giải đấu)
// =============================

// League item chung (dùng cho popular và trong countries/international)
export interface League {
  id: number;
  name: string;
  localizedName: string;
  pageUrl: string;
  country_code: string;
}

// Country hoặc International group
export interface LeagueGroup {
  ccode: string;
  name: string;
  localizedName: string;
  leagues: League[];
}

// Data container cho danh sách league
export interface LeagueData {
  popular: League[];
  international: LeagueGroup[];
  countries: LeagueGroup[];
}

// Root response cho list league
export interface LeagueResponse {
  total: number;
  limit: number;
  offset: number;
  data: LeagueData;
}

export interface LeagueResponseSearch {
  popular: League[];
  international: LeagueGroup;   // ❗ chỉ là object, không phải array
  countries: LeagueGroup[];
}

// Thông tin chi tiết 1 league
export interface LeagueDetails {
  league_id: string;
  name: string;
  country_code: string;
  country_name: string;
  data: {
    id: number;
    name: string;
    localizedName: string;
    pageUrl: string;
  };
  created_at: string; // ISO string
  updated_at: string; // ISO string
}

// =============================
// Standings (bảng xếp hạng)
// =============================

export interface LeagueStandingResponse {
  data: {
    ccode: string; // "ENG"
    leagueId: number; // 47
    pageUrl: string;
    leagueName: string;
    legend: LegendItem[];
    ongoing: OngoingItem[];
    table: {
      all: StandingTeam[];
      // có thể thêm home/away nếu API trả
    };
  };
}

export interface LegendItem {
  title: string;     // "Champions League"
  tKey: string;      // "championsleague"
  color: string;     // "#2AD572"
  indices: number[]; // [0,1,2,3]
}

export interface StandingTeam {
  name: string;
  shortName: string;
  id: number;
  pageUrl: string;
  deduction: number | null;
  ongoing: OngoingItem | null;
  played: number;
  wins: number;
  draws: number;
  losses: number;
  scoresStr: string;
  goalConDiff: number;
  pts: number;
  idx: number;       // vị trí BXH
  qualColor?: string;
}

// nếu cần refine ongoing thì define rõ structure
export type OngoingItem = {
  type: string;
  message: string;
} | string; // fallback: có thể API trả string

// =============================
// Matches (lịch thi đấu)
// =============================

export interface MatchesResponse {
  date: string;   // "20251004"
  total: number;  // 10
  limit: number;  // 200
  offset: number; // 0
  matches: Record<string, MatchItem[]>; 
}

export interface MatchItem {
  match: MatchSummary;
  details: MatchDetails;
  details_source: string; // ví dụ "postgres"
  league: LeagueInfo;
}

export interface MatchSummary {
  match_id: string;
  date: string;
  league_id: string;
  home_team: string;
  away_team: string;
  status: string;
  data: MatchData;
  details_crawled: boolean;
}

export interface MatchData {
  id: number;
  leagueId: number;
  time: string;
  home: TeamInfo;
  away: TeamInfo;
  eliminatedTeamId: number | null;
  statusId: number;
  tournamentStage: string;
  status: MatchStatus;
  timeTS: number;
}

export interface TeamInfo {
  id: number;
  score: number;
  name: string;
  longName: string;
}

export interface MatchStatus {
  utcTime: string;
  halfs: {
    firstHalfStarted: string;
  };
  periodLength: number;
  started: boolean;
  cancelled: boolean;
  finished: boolean;
}

// Chi tiết match - thay vì any, define rõ hoặc để unknown
export type MatchDetails = Record<string, never>; 
// hoặc: export type MatchDetails = {}; (nếu API chưa có dữ liệu)

// LeagueInfo (gọn lại từ LeagueDetails nhưng dùng trong match item)
export interface LeagueInfo {
  league_id: string;
  name: string;
  country_code: string;
  country_name: string;
}
