"use client";

import {
  FILTER_TABS,
  SPORTS_TABS,
} from "@/constants/sports";
import { useEffect, useState, useCallback } from "react";

import BannerCarousel from "@/components/common/BannerCarousel";
import BannerOdds from "@/components/features/BannerOdds";
import Image from "next/image";
import Introduction from "@/components/common/Introduction";
import Link from "next/link";
import MatchCard from "@/components/common/MatchCard";
import MatchCardSkeleton from "@/components/common/MatchCardSkeleton";
import OrderVideo from "@/components/features/OrderVideo";
import { getActiveBanners } from "@/data/banners";
import { useMatchesWithOrder } from "@/hooks/useMatchesWithOrder";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSupabase } from "@/contexts/SupabaseContext";
import { BettingService } from "@/services/betting.service";
import { BannerData } from "@/types/banner";

export default function HomePage() {
  const [activeTab, setActiveTab] = useState(0);
  const [activeFilterTab, setActiveFilterTab] = useState(0);
  const [bettingOdds, setBettingOdds] = useState<BannerData[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);

  const isMobile = useIsMobile();
  const { supabase } = useSupabase();

  const {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    filterCounts,
    // Live polling related
    isPollingLive,
    liveMatchesCount,
    lastLiveUpdate,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatchesWithOrder();

  // Fetch betting odds for mobile
  const fetchBettingOdds = useCallback(async () => {
    if (!isMobile) return;

    try {
      const bettingService = new BettingService(supabase);
      const data = await bettingService.getLatestBettingOdds(5);
      if (data) {
        // Convert BettingOdds to BannerData format
        const bannerData: BannerData[] = data.map((odds) => ({
          id: odds.id,
          expert: odds.expert,
          tournament: odds.tournament,
          team_name: odds.team_name,
          closing_bet: odds.closing_bet,
          saying: odds.saying,
          status: odds.status,
          created_at: odds.created_at,
          updated_at: odds.updated_at,
          created_by: odds.created_by,
          sent_at: odds.sent_at,
          type: odds.type,
        }));
        setBettingOdds(bannerData);
      }
    } catch (error) {
      console.error("Error fetching betting odds:", error);
    }
  }, [isMobile, supabase]);

  useEffect(() => {
    fetchMatchesByFilter(FILTER_TABS[0].filterType, SPORTS_TABS[0].category);
  }, []);

  useEffect(() => {
    fetchAllCounts("football");
  }, [fetchAllCounts]);

  useEffect(() => {
    fetchMatchesByFilter(
      FILTER_TABS[activeFilterTab].filterType,
      SPORTS_TABS[activeTab].category
    );
  }, [activeTab, activeFilterTab, fetchMatchesByFilter]);

  useEffect(() => {
    fetchBettingOdds();
  }, [fetchBettingOdds]);

  // Auto slide betting odds banners
  useEffect(() => {
    if (!isMobile || bettingOdds.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentBannerIndex((prev) => (prev + 1) % bettingOdds.length);
    }, 5000); // Change banner every 5 seconds

    return () => clearInterval(interval);
  }, [isMobile, bettingOdds.length]);

  // Handle manual navigation
  const handlePrevBanner = useCallback(() => {
    setCurrentBannerIndex((prev) =>
      prev === 0 ? bettingOdds.length - 1 : prev - 1
    );
  }, [bettingOdds.length]);

  const handleNextBanner = useCallback(() => {
    setCurrentBannerIndex((prev) => (prev + 1) % bettingOdds.length);
  }, [bettingOdds.length]);

  return (
    <>
      {/* Top Banner - Chạy tràn toàn màn hình */}
      <section className="py-2 banner-mobile-breakout">
        <Link href="/khuyen-mai">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 sm:p-3 text-white text-center overflow-hidden relative">
            {/* Animated text container */}
            <div className="animate-marquee whitespace-nowrap">
              <span className="inline-block mr-8">
                <span className="text-lg font-bold">🔥 HOT</span>
                <span className="text-lg ml-2">Chỉ có tại NGOẠI HẠNG TV</span>
                <span className="text-lg ml-2">
                  với nhà tài trợ KUDV liên hệ ngay CSKH để nhận ngay khuyến mãi nạp lần đầu lên tới 1688k.
                  <span className="font-bold">
                    {" "}
                    Quà tặng tân thủ lên tới 10 triệu đồng trog tháng. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu.
                  </span>
                </span>
              </span>
              {/* Duplicate for seamless loop */}
              <span className="inline-block mr-8">
                <span className="text-lg font-bold">🔥 HOT</span>
                <span className="text-lg ml-2">Chỉ tại NGOẠI HẠNG TV</span>
                <span className="text-lg ml-2">
                  với nhà tài trợ KUDV liên hệ ngay CSKH để nhận ngay khuyến mãi nạp lần đầu lên tới 1688k.
                  <span className="font-bold">
                    {" "}
                    Quà tặng tân thủ lên tới 10 triệu đồng trog tháng. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu.
                  </span>
                </span>
              </span>
            </div>
          </div>
        </Link>
      </section>

      <div className="w-full max-w-[1600px] mx-auto">
        {/* Live Room Video Section with Side Banners */}
        <section className="sm:pb-8">
          {/* Video Player with Side Banners */}
          <div className="grid grid-cols-1 lg:grid-cols-4 sm:gap-4 lg:gap-1">
            {/* Video Player - Center (4 columns) */}
            <div className="lg:col-span-3">
              <div className="relative w-full">
                {/* Order Video Component */}
                <OrderVideo
                  className="match-card-enhanced"
                />
              </div>
            </div>

            {/* Right Banner */}
            <div className="hidden lg:block">
              <div className="sticky top-4 h-full">
                <BannerCarousel
                  banners={getActiveBanners()}
                  interval={4000}
                  className="h-full w-full"
                  showInfo={true}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Banner Carousel */}
        <section className="lg:hidden">
          <BannerCarousel
            banners={getActiveBanners()}
            interval={10000}
            className="w-full"
            showInfo={false}
          />
        </section>

        {/* Sports Tabs */}
        <section className="">
          <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto no-scrollbar pb-2">
            {SPORTS_TABS.map((tab, index) => (
              <button
                key={tab.label}
                onClick={() => {
                  setActiveTab(index);
                  // State change will trigger useEffect to fetch data
                }}
                className={`flex-shrink-0 rounded-full border px-2 sm:px-4 py-1 sm:py-1.5 text-xs sm:text-sm transition-colors flex items-center gap-2 ${activeTab === index
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                  : "border-gray-200 dark:border-custom-dark-secondary bg-white dark:bg-custom-dark text-gray-700 dark:text-custom-muted hover:bg-gray-50 dark:hover:bg-custom-dark-secondary"
                  }`}
              >
                <Image
                  src={activeTab === index ? tab.iconActive : tab.icon}
                  alt={tab.label}
                  width={20}
                  height={20}
                  className="w-5 h-5"
                  priority
                />
                {tab.label}
              </button>
            ))}
          </div>
        </section>

        {/* Filter Tabs */}
        <section className="">
          <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto no-scrollbar pb-2">
            {FILTER_TABS.map((tab, index) => {
              const getCount = () => {
                return filterCounts[tab.filterType] || 0;
              };

              const count = getCount();

              return (
                <button
                  key={tab.label}
                  onClick={() => {
                    setActiveFilterTab(index);
                  }}
                  className={`flex-shrink-0 rounded-full border px-2 sm:px-4 py-1 sm:py-1.5 text-xs sm:text-sm transition-colors flex items-center gap-2 ${activeFilterTab === index
                    ? "bg-blue-500 text-white border-blue-500"
                    : "border-gray-200 dark:border-custom-dark-secondary bg-white dark:bg-custom-dark text-gray-700 dark:text-custom-muted hover:bg-gray-50 dark:hover:bg-custom-dark-secondary"
                    }`}
                >
                  {tab.icon && (
                    <Image
                      src={
                        activeFilterTab === index ? tab.iconActive : tab.icon
                      }
                      alt={tab.label}
                      width={16}
                      height={16}
                      className="w-4 h-4"
                      priority
                    />
                  )}

                  <span>{tab.label}</span>

                  {/* Count badge */}
                  {count > 0 && (
                    <span
                      className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 rounded-full text-xs font-medium ${activeFilterTab === index
                        ? "bg-white/20 text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                        }`}
                    >
                      {count}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </section>

        {/* Matches grid */}
        <section className="pb-6 sm:pb-12">
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
              {FILTER_TABS[activeFilterTab]?.label || "Trận đấu"}{" "}
              {SPORTS_TABS[activeTab]?.label
                ? `- ${SPORTS_TABS[activeTab].label}`
                : ""}
            </h2>

            {/* Live Polling Indicator */}
            {isPollingLive && liveMatchesCount > 0 && (
              <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Live ({liveMatchesCount})</span>
                </div>
                {lastLiveUpdate && (
                  <span className="text-gray-500 dark:text-gray-400">
                    Cập nhật: {lastLiveUpdate.toLocaleTimeString()}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center justify-between">
                <p className="text-red-700 dark:text-red-400 text-sm">
                  {error}
                </p>
                <button
                  onClick={clearError}
                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                >
                  ✕
                </button>
              </div>
            </div>
          )}

          {/* Loading State with Skeleton */}
          {loading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <MatchCardSkeleton
                  key={`skeleton-${index}`}
                  variant="detailed"
                  className="animate-match-fade-in"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animationFillMode: "both",
                  }}
                />
              ))}
            </div>
          )}

          {/* Matches Grid */}
          {!loading && matches.length > 0 && (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-4">
                {matches.map((match, index) => {
                  // Render match card
                  const matchCard = (
                    <MatchCard
                      key={match.id}
                      match={match}
                      variant="detailed"
                      className="animate-match-fade-in"
                      hasOrder={match.hasOrder}
                      style={{
                        animationDelay: `${index * 50}ms`,
                        animationFillMode: "both",
                      }}
                    />
                  );

                  // Insert BettingOdds after first card on mobile
                  if (isMobile && index === 0 && bettingOdds.length > 0) {
                    return [
                      matchCard,
                      <div key="betting-odds-section" className="col-span-1">
                        <div className="relative w-full overflow-hidden rounded-lg" style={{ minHeight: '108px' }}>
                          {/* Slides container */}
                          <div
                            className="flex transition-transform duration-500 ease-in-out"
                            style={{ transform: `translateX(-${currentBannerIndex * 100}%)` }}
                          >
                            {bettingOdds.map((odds) => (
                              <div key={odds.id} className="w-full flex-shrink-0">
                                <BannerOdds currentBanner={odds} isSlide={true} />
                              </div>
                            ))}
                          </div>

                          {/* Navigation buttons */}
                          {bettingOdds.length > 1 && (
                            <>
                              <button
                                onClick={handlePrevBanner}
                                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all z-20"
                                aria-label="Previous banner"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                              </button>
                              <button
                                onClick={handleNextBanner}
                                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all z-20"
                                aria-label="Next banner"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                              </button>

                              {/* Dots indicator */}
                              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
                                {bettingOdds.map((_, idx) => (
                                  <button
                                    key={idx}
                                    onClick={() => setCurrentBannerIndex(idx)}
                                    className={`w-2 h-2 rounded-full transition-all ${
                                      idx === currentBannerIndex
                                        ? 'bg-white w-4'
                                        : 'bg-white/50 hover:bg-white/75'
                                    }`}
                                    aria-label={`Go to banner ${idx + 1}`}
                                  />
                                ))}
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    ];
                  }

                  return matchCard;
                })}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="mt-6 text-center">
                  <button
                    onClick={loadMore}
                    disabled={loadingMore}
                    className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
                  >
                    {loadingMore ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Đang tải...
                      </>
                    ) : (
                      "Xem thêm"
                    )}
                  </button>
                </div>
              )}

              {/* Loading More Skeleton */}
              {loadingMore && (
                <div className="mt-4 grid grid-cols-1 lg:grid-cols-3 gap-2 sm:gap-3 lg:gap-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <MatchCardSkeleton
                      key={`loading-more-skeleton-${index}`}
                      variant="detailed"
                      className="animate-match-fade-in"
                      style={{
                        animationDelay: `${index * 50}ms`,
                        animationFillMode: "both",
                      }}
                    />
                  ))}
                </div>
              )}
            </>
          )}

          {/* Empty State */}
          {!loading && matches.length === 0 && !error && (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                Không có trận đấu nào
              </p>
            </div>
          )}
        </section>
      </div>
      {/* Introduction  */}
      <Introduction/>
      {/* Bottom Menu - Mobile Only */}
      
    </>
  );
}