export interface ChatRoom {
    id: string;
    name: string;
    type: "direct" | "group" | "general" | "private";
    last_message?: string;
    last_message_at?: string;
    unread_count?: number;
    other_user?: {
        id: string;
        full_name: string;
        avatar_url?: string;
    };
}

export interface ChatMessage {
    id: string;
    content: string;
    created_at: string;
    user_id: string;
    display_name: string;
    anon_session_id?: string;
    user: {
        full_name: string;
        avatar_url?: string;
        role?: string;
    };
}

export interface CreateChatData {
    name: string;
    type: "direct" | "group" | "private";
    members?: string[];
}

export interface ChatResponse {
    data: ChatRoom | null;
    error: Error | null;
}

export interface MessagesResponse {
    messages: ChatMessage[];
    error: Error | null;
}

export interface Channel {
    id: string;
    name: string;
    type: "public" | "private";
    created_at: string;
    updated_at: string;
    channel_id: string;
    channel_name: string;
    channel_type: "public" | "private";
    other_user_name?: string;
    other_user_avatar?: string;
    unread_count?: number;
    last_message?: string;
    last_message_at?: string;
}

export interface Message {
    id: string;
    content: string;
    user_id: string;
    created_at: string;
    status?: "sent" | "delivered" | "read";
    profiles: {
        full_name?: string;
        email: string;
        avatar_url?: string;
    };
    file_attachments?: {
        id: string;
        filename: string;
        file_url: string;
        file_type: string;
        file_size: number;
    }[];
}