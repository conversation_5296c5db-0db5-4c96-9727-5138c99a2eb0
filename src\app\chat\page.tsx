"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import AuthModal from "@/components/AuthModal";

import ChatInput from "@/components/features/ChatInput";

interface MockMessage {
  id: string;
  content: string;
  timestamp: number;
  isAdmin: boolean;
  senderName: string;
}

const baseTimestamp = 1703000000000; // Fixed base timestamp

const mockMessages: MockMessage[] = [
  {
    id: "1",
    content: "Xin chào! Tôi có thể giúp gì cho bạn hôm nay?",
    timestamp: baseTimestamp - 3600000, // 1 hour ago
    isAdmin: true,
    senderName: "Admin Support",
  },
  {
    id: "2",
    content: "Chào admin, tôi muốn hỏi về việc nạp tiền vào tài khoản",
    timestamp: baseTimestamp - 3500000,
    isAdmin: false,
    senderName: "<PERSON>h<PERSON><PERSON> hàng",
  },
  {
    id: "3",
    content:
      "D<PERSON>, bạn có thể nạp tiền qua các phương thức: <PERSON>yển khoản ngân hàng, Ví điện tử (MoMo, ZaloPay), hoặc thẻ cào điện thoại. Bạn muốn sử dụng phương thức nào?",
    timestamp: baseTimestamp - 3400000,
    isAdmin: true,
    senderName: "Admin Support",
  },
  {
    id: "4",
    content:
      "Tôi muốn chuyển khoản ngân hàng. Thông tin tài khoản như thế nào?",
    timestamp: baseTimestamp - 3300000,
    isAdmin: false,
    senderName: "Khách hàng",
  },
];

export default function CustomerSupportPage() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<MockMessage[]>(mockMessages);

  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [isTyping, setIsTyping] = useState(false);

  // Mobile viewport handling
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [_isChatInView, setIsChatInView] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const chatWrapperRef = useRef<HTMLDivElement>(null);
  const isLoggedIn = !!user;

  // Chặn scroll của body, chỉ cho phép scroll trong Chat Container
  useEffect(() => {
    const html = document.documentElement;
    const body = document.body;
    const prevHtmlOverflow = html.style.overflow;
    const prevBodyOverflow = body.style.overflow;
    const prevHtmlOverscroll = html.style.overscrollBehavior as string;
    html.style.overflow = "hidden";
    body.style.overflow = "hidden";
    html.style.overscrollBehavior = "contain";

    return () => {
      html.style.overflow = prevHtmlOverflow;
      body.style.overflow = prevBodyOverflow;
      html.style.overscrollBehavior = prevHtmlOverscroll;
    };
  }, []);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setIsMobile(vw < 1024);

    // Detect iOS
    const isIOSDevice =
      /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(
            0,
            window.innerHeight - visualViewport.height
          );
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();

    // Listen for viewport changes
    const handleResize = () => updateViewportInfo();
    const handleOrientationChange = () => {
      setTimeout(updateViewportInfo, 100); // Delay to get accurate measurements
    };

    // Listen for visual viewport changes (keyboard on mobile)
    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        updateViewportInfo();
      }
    };

    // iOS specific keyboard events
    const handleIOSKeyboardShow = () => {
      setIsKeyboardOpen(true);
      setTimeout(updateViewportInfo, 100);
    };

    const handleIOSKeyboardHide = () => {
      setIsKeyboardOpen(false);
      setTimeout(updateViewportInfo, 100);
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleOrientationChange);

    // iOS keyboard events
    if (isIOS) {
      window.addEventListener("focusin", handleIOSKeyboardShow);
      window.addEventListener("focusout", handleIOSKeyboardHide);
    }

    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        handleVisualViewportChange
      );
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);

      if (isIOS) {
        window.removeEventListener("focusin", handleIOSKeyboardShow);
        window.removeEventListener("focusout", handleIOSKeyboardHide);
      }

      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleVisualViewportChange
        );
      }
    };
  }, [updateViewportInfo, isIOS]);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    const timer = setTimeout(() => {
      if (chatContainerRef.current) {
        const container = chatContainerRef.current;
        container.scrollTop = container.scrollHeight;
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [messages]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 300); // Delay to ensure keyboard is fully open
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  // Intersection Observer để theo dõi chat container
  useEffect(() => {
    if (!chatWrapperRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        setIsChatInView(entry.isIntersecting);
      },
      {
        threshold: 0.3, // Trigger khi 30% của chat container visible
      }
    );

    observer.observe(chatWrapperRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);
    }
  }, []);

  const handleSendMessage = (messageText: string) => {
    if (!isLoggedIn) {
      setIsAuthModalOpen(true);
      return;
    }

    if (!messageText.trim()) return;

    // Add user message
    const now = Date.now();
    const userMessage: MockMessage = {
      id: `user_${now}`,
      content: messageText,
      timestamp: now,
      isAdmin: false,
      senderName:
        user?.user_metadata?.full_name || user?.email?.split("@")[0] || "Bạn",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Show typing indicator
    setIsTyping(true);

    // Simulate admin response after 2 seconds
    setTimeout(() => {
      setIsTyping(false);
      const responseTime = Date.now();
      const adminResponse: MockMessage = {
        id: `admin_${responseTime}`,
        content:
          "Cảm ơn bạn đã liên hệ! Chúng tôi sẽ xử lý yêu cầu của bạn trong thời gian sớm nhất.",
        timestamp: responseTime,
        isAdmin: true,
        senderName: "Admin Support",
      };
      setMessages((prev) => [...prev, adminResponse]);
    }, 2000);
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString("vi-VN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const openAuthModal = (mode: "login" | "register") => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  // Calculate dynamic styles for mobile
  const getChatContainerStyle = () => {
    const baseBottomSpace = isMobile ? 400 : 72;
    const extraKeyboardSpace = isMobile && isKeyboardOpen ? keyboardHeight : 0;
    return {
      paddingBottom: `${baseBottomSpace + extraKeyboardSpace}px`,
      WebkitOverflowScrolling: "touch" as const,
    };
  };

  return (
    <div
      data-chat-container
      ref={chatWrapperRef}
      className="my-4 bg-gray-50 dark:bg-gray-900 border rounded-md overflow-hidden"
    >
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 dark:border-gray-700 border-b shadow-md">
        <div className="mx-auto py-1 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div>
                <h1 className="lg:text-xl text-lg font-semibold text-gray-900 dark:text-white">
                  Chăm sóc khách hàng
                </h1>
                <p className="text-gray-600 text-sm dark:text-gray-400">
                  Hỗ trợ 24/7 - Phản hồi nhanh chóng
                </p>
              </div>
            </div>

            {/* Admin Status & Stats */}
            <div className="flex items-center">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Online
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          {/* Chat Messages */}
          <div
            ref={chatContainerRef}
            className={`overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900 scroll-smooth scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-transparent hover:scrollbar-thumb-gray-500 ${
              isMobile ? "h-[80vh]" : "h-[550px]"
            }`}
            style={getChatContainerStyle()}
            onScroll={handleScroll}
          >
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.isAdmin ? "justify-start" : "justify-end"
                }`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm animate-in slide-in-from-bottom-2 duration-300 ${
                    message.isAdmin
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      : "bg-blue-500 text-white"
                  }`}
                >
                  <div className="flex items-center mb-1">
                    <span
                      className={`text-xs font-medium opacity-75 ${
                        message.senderName === "Admin Support"
                          ? "text-red-500"
                          : "text-black"
                      }`}
                    >
                      {message.senderName}
                    </span>
                  </div>
                  <p className="text-sm whitespace-pre-wrap">
                    {message.content}
                  </p>
                  <p className="text-xs opacity-75 mt-1 text-right">
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <div className="flex items-center mb-1">
                    <span className="text-xs font-medium opacity-75">
                      Admin Support
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce [animation-delay:150ms]"></div>
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce [animation-delay:300ms]"></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
            {showScrollToBottom && (
              <button
                type="button"
                onClick={scrollToBottom}
                title="Scroll to bottom"
                className="absolute bottom-32 right-8 bg-black/30 hover:bg-black text-white p-2 rounded-full shadow-lg transition-all duration-200 z-10"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
              </button>
            )}
          </div>

          {/* Chat Input */}
          <div
            className="fixed bottom-14 lg:bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg"
            style={{
              // bottom: isMobile && isKeyboardOpen ? `${keyboardHeight}px` : 0,
              paddingBottom: "env(safe-area-inset-bottom, 200px)",
            }}
          >
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoggedIn={isLoggedIn}
              onOpenAuthModal={openAuthModal}
              placeholder="Nhập tin nhắn..."
              noBorderRadius={false}
            />
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        initialMode={authMode}
        onModeChange={setAuthMode}
        onLoginSuccess={() => {
          setIsAuthModalOpen(false);
        }}
      />
    </div>
  );
}
