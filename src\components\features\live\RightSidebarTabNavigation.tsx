"use client";

import Image from "next/image";
import { getTabsConfig } from "@/constants/tabs";
import { MobileScreenLockButton } from "@/app/truc-tiep/[match]/[streamer]/[id]/MobileComponents";
import { useScreenLock } from "@/components/ScreenLockContext";

interface RightSidebarTabNavigationProps {
  activeTab: number;
  isMobile: boolean;
  handleTabChange: (index: number) => void;
}

export default function RightSidebarTabNavigation({
  activeTab,
  isMobile,
  handleTabChange,
}: RightSidebarTabNavigationProps) {
  const tabs = getTabsConfig(isMobile);
  const { isScreenLocked, setIsScreenLocked } = useScreenLock();

  const toggleScreenLock = () => {
    setIsScreenLocked(!isScreenLocked);
  };

  return (
    <div className="p-2 overflow-x-auto">
      <div className="flex min-w-max gap-1">
        {tabs.map((tab) => (
          <button
            key={tab.index}
            onClick={() => handleTabChange(tab.index)}
            className={`flex-shrink-0 px-1 sm:px-2 py-0 text-center text-xs font-medium transition-all duration-200 whitespace-nowrap rounded-full flex items-center gap-0 ${
              tab.index === 2 && activeTab === tab.index
                ? "text-orange-600 border-2 border-orange-600 bg-orange-50 dark:bg-orange-900/20 shadow-sm ring-orange-300 blink-slow"
                : activeTab === tab.index
                ? "text-blue-600 border-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20 shadow-sm"
                : "text-gray-500 dark:text-gray-400 border-0 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-white dark:hover:bg-gray-700"
            }`}
          >
            {activeTab === tab.index && (
              <span className="font-large text-sm sm:text-md">{tab.label}</span>
            )}
            <div className="w-8 h-6 sm:w-10 sm:h-7 flex items-center justify-center">
              <Image
                src={activeTab === tab.index ? tab.iconActive : tab.icon}
                alt={tab.label}
                width={30}
                height={30}
                className={`w-6 h-6 sm:w-8 sm:h-8 transition-all  ${tab.index === 2 && activeTab === tab.index ? "blink-slow" : ""}`}
              />
            </div>
          </button>
        ))}
        <MobileScreenLockButton
          isScreenLocked={isScreenLocked}
          toggleScreenLock={toggleScreenLock}
        />
      </div>
    </div>
  );
}
