"use client";

import { useCallback, useEffect, useRef, useState } from "react";

import AuthModal from "./AuthModal";
import Image from "next/image";
import Link from "next/link";
import { useScreenLock } from "./ScreenLockContext";
import { useTheme } from "./ThemeProvider";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
// Define a User type for the user object
interface User {
  isLoggedIn: boolean;
  photoURL?: string;
  name?: string;
}

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const { isScreenLocked } = useScreenLock();
  const { user, profile, signOut } = useAuth();
  const isDarkMode = theme === "dark";

  const router = useRouter();
  // Ref và height để giữ header cố định khi scroll và tránh chồng lên nội dung
  const headerRef = useRef<HTMLDivElement | null>(null);
  const [headerHeight, setHeaderHeight] = useState(0);
  useEffect(() => {
    const updateHeight = () => {
      if (headerRef.current) {
        setHeaderHeight(headerRef.current.offsetHeight);
      }
    };
    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);
  // Tạo userData từ AuthContext thay vì localStorage
  const userData: User | null = user
    ? {
        isLoggedIn: true,
        photoURL: profile?.avatar_url || user.user_metadata?.avatar_url,
        name: profile?.full_name || user.user_metadata?.full_name || user.email,
      }
    : null;

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Refs để phát hiện click ngoài và đóng menu
  const mobileMenuRef = useRef<HTMLDivElement | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Node;

      if (
        isMobileMenuOpen &&
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(target)
      ) {
        setIsMobileMenuOpen(false);
      }

      if (
        isDropdownOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(target)
      ) {
        setIsDropdownOpen(false);
      }
    };

    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsMobileMenuOpen(false);
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEsc);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEsc);
    };
  }, [isMobileMenuOpen, isDropdownOpen]);

  const handleLoginSuccess = useCallback((user: User) => {
    // Không cần setUserData nữa vì userData được tạo từ AuthContext
  }, []);

  const openAuthModal = useCallback((mode: "login" | "register") => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
    setIsMobileMenuOpen(false);
  }, []);

  const closeAuthModal = useCallback(() => {
    setIsAuthModalOpen(false);
    // Reset authMode về login khi đóng modal
    setAuthMode("login");
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      setIsDropdownOpen(false);
      router.push("/");
    } catch (error) {
      // Error during logout
      setIsDropdownOpen(false);
    }
  }, [signOut, router]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Tránh hydration mismatch: chỉ ẩn header sau khi client mount
  if (isClient && isScreenLocked) {
    return null;
  }

  return (
    <div>
      {/* Header */}
      <header
        ref={headerRef}
        className="fixed top-0 left-0 right-0 z-[2000] border-b border-gray-200 dark:border-custom-dark-secondary bg-white/90 dark:bg-custom-dark/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 dark:supports-[backdrop-filter]:bg-custom-dark/70 shadow-sm"
      >
        <div className="mx-auto flex max-w-[1600px] items-center justify-between gap-4 lg:py-3 px-4">
          {/* Left side: Mobile menu button + Logo */}
          <div className="flex items-center gap-3">
            {/* Mobile menu button - Left side */}
            <button
              title="menu"
              onMouseDown={(e) => e.stopPropagation()}
              onTouchStart={(e) => e.stopPropagation()}
              onClick={toggleMobileMenu}
              className="md:hidden rounded-lg p-2 text-gray-600 dark:text-custom-muted hover:bg-gray-100 dark:hover:bg-custom-dark-secondary transition-colors"
            >
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>

            {/* Logo */}
            <Link href="/" className="flex items-center gap-3">
              <Image
                src="/ngoaihangtv.png"
                alt="NGOAIHANG TV Logo"
                width={220}
                height={32}
                priority
                // style={{ width: 'auto', height: 'auto' }}
              />
            </Link>
          </div>

          {/* Center: Navigation (hidden on mobile) */}
          <nav className="hidden md:flex items-center gap-5 text-sm font-semibold text-gray-700 dark:text-custom-muted">
            {/* <Link
              href="/dien-dan"
              className="block px-4 py-3 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-b-lg"
            >
              DIỄN ĐÀN
            </Link> */}
            {/* <Link
              href="/lich-truc-tiep/bong-da"
              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              LỊCH TRỰC TIẾP
            </Link> */}
            {/* <Link
              href="/ty-so-cac-tran-va-giai-dau"
              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              TỶ SỐ

            </Link> */}
            {
              <Link
                href="/bxh-va-lich-thi-dau"
                className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                BXH & LỊCH THI ĐẤU
              </Link>
            }
            <Link
              href="/khuyen-mai"
              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              KHUYẾN MÃI
            </Link>
            <Link
              href="/chat"
              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              CHAT
            </Link>

            <Link
              href="/tin-tuc"
              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              TIN TỨC
            </Link>

            {/* TIN TỨC & TUYỂN DỤNG Dropdown */}
            {/* <div className="relative group">
              <button className="flex items-center gap-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                TIN TỨC & TUYỂN DỤNG
                <svg className="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              
              <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-custom-dark border border-gray-200 dark:border-custom-dark-secondary rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <Link href="/tin-tuc" className="block px-4 py-3 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-t-lg">
                  TIN TỨC
                </Link>
              </div>
            </div> */}
          </nav>

          {/* Right side: Theme toggle + Auth buttons */}
          <div className="flex items-center gap-3">
            {/* Theme toggle button */}
            <button
              onClick={toggleTheme}
              className="rounded-lg p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-custom-dark-secondary dark:text-custom-muted transition-colors"
              title={
                isClient && isDarkMode
                  ? "Chuyển sang chế độ sáng"
                  : "Chuyển sang chế độ tối"
              }
            >
              {isClient && isDarkMode ? (
                // Sun icon for dark mode
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              ) : (
                // Moon icon for light mode
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                  />
                </svg>
              )}
            </button>

            {/* Auth buttons */}
            {isClient && userData && userData.isLoggedIn ? (
              <div ref={dropdownRef} className="avatar-container relative">
                <div onClick={toggleDropdown} className="cursor-pointer">
                  {userData.photoURL ? (
                    <img
                      src={userData.photoURL}
                      alt="User Avatar"
                      className="avatar rounded-full w-10 h-10"
                    />
                  ) : (
                    <div className="avatar rounded-full w-10 h-10 flex items-center justify-center bg-gray-400 text-white font-bold">
                      {userData.name
                        ? userData.name.charAt(0).toUpperCase()
                        : user?.email
                        ? user.email.charAt(0).toUpperCase()
                        : "U"}
                    </div>
                  )}
                </div>

                {isDropdownOpen && (
                  <div
                    onClick={() => setIsDropdownOpen(false)}
                    className="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded shadow-lg z-10"
                  >
                    <Link
                      href="/profile"
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Profile
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Đăng xuất
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="hidden md:flex items-center gap-2">
                <button
                  onClick={() => openAuthModal("login")}
                  className="rounded-lg px-4 py-2 text-sm font-medium text-gray-700 dark:text-custom-muted hover:bg-gray-100 dark:hover:bg-custom-dark-secondary transition-colors"
                >
                  ĐĂNG NHẬP
                </button>
                <button
                  onClick={() => openAuthModal("register")}
                  className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700 transition-colors"
                >
                  ĐĂNG KÝ
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Menu Dropdown */}
        {isMobileMenuOpen && (
          <>
            {/* Backdrop to capture outside clicks */}
            <div
              className="fixed inset-0 z-[1990]"
              onClick={() => setIsMobileMenuOpen(false)}
            />
            <div
              ref={mobileMenuRef}
              className="absolute top-full left-0 right-0 md:hidden border-t border-gray-200 dark:border-custom-dark-secondary bg-white dark:bg-custom-dark shadow-lg z-[2001]"
            >
              <div className="px-4 py-3 space-y-3">
                {/* Navigation Links */}
                <div className="space-y-2">
                  {/* <Link
                  href="/dien-dan"
                  className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary rounded-lg transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  DIỄN ĐÀN
                </Link> */}
                  {/* <Link
                  href="/lich-truc-tiep/bong-da"
                  className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary rounded-lg transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  LỊCH TRỰC TIẾP
                </Link> */}
                  {/* <Link
                  href="/ty-so-cac-tran-va-giai-dau"
                  className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary rounded-lg transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  TỶ SỐ
                </Link> */}
                <Link
                  href="/chat"
                  className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary hover:text-blue-600 dark:hover:text-blue-400 rounded-lg transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  CHAT
                </Link>
                  <Link
                    href="/bxh-va-lich-thi-dau"
                    className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary rounded-lg transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    BXH & LỊCH THI ĐẤU
                  </Link>
                  <Link
                    href="/khuyen-mai"
                    className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary hover:text-blue-600 dark:hover:text-blue-400 rounded-lg transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    KHUYẾN MÃI
                  </Link>
                  <Link
                    href="/tin-tuc"
                    className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted hover:bg-white dark:hover:bg-custom-dark-secondary rounded-lg transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    TIN TỨC
                  </Link>
                </div>

                {/* Divider */}
                <div className="border-t border-gray-200 dark:border-custom-dark-secondary my-3"></div>

                {/* Auth Buttons for Mobile */}
                {isClient && userData && userData.isLoggedIn ? (
                  <div className="space-y-2">
                    <Link
                      href="/profile"
                      className="block px-3 py-2 text-sm text-gray-700 dark:text-custom-muted bg-gray-100 dark:bg-custom-dark-secondary hover:bg-gray-200 dark:hover:bg-custom-subtle rounded-lg transition-colors border border-gray-200 dark:border-custom-dark-secondary"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-custom-muted bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors border border-red-200 dark:border-red-800"
                    >
                      Đăng xuất
                    </button>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={() => openAuthModal("login")}
                      className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 dark:text-custom-muted bg-gray-100 dark:bg-custom-dark-secondary hover:bg-gray-200 dark:hover:bg-custom-subtle rounded-lg transition-colors border border-gray-200 dark:border-custom-dark-secondary"
                    >
                      Đăng nhập
                    </button>
                    <button
                      onClick={() => openAuthModal("register")}
                      className="flex-1 px-3 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                    >
                      Đăng ký
                    </button>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </header>

      {/* Spacer để tránh nội dung bị che bởi header fixed */}
      <div style={{ height: headerHeight }} />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        initialMode={authMode}
        onLoginSuccess={handleLoginSuccess}
      />
    </div>
  );
}
