"use client";

import { useState, useRef, useEffect, useCallback } from "react";

export default function FloatingContact() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Khởi tạo vị trí mặc định (góc phải dưới của chat section)
  useEffect(() => {
    setIsClient(true);
    const updatePosition = () => {
      // Tìm chat section container
      // Tìm chat container bằng cách query từ document
      let chatContainer = document.querySelector(".chat-section-container");

      // Nếu không tìm thấy, fallback về container hiện tại
      if (!chatContainer && containerRef.current) {
        chatContainer = containerRef.current;
      }

      if (chatContainer) {
        const rect = chatContainer.getBoundingClientRect();
        setPosition({
          x: rect.width - 80, // 40px từ phải của chat section
          y: rect.height / 3 , // 40px từ dưới của chat section (góc dưới cùng)
        });
      } else {
        // Fallback nếu không tìm thấy chat container
        setPosition({
          x: 260, // Giả sử chat section rộng khoảng 300px
          y: 560, // Giả sử chat section cao khoảng 600px (góc dưới cùng)
        });
      }
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);
    return () => window.removeEventListener("resize", updatePosition);
  }, []);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleTelegram = () => {
    // Mở Telegram
    window.open("https://t.me/thuphuongepl", "_blank");
  };

  const handleZalo = () => {
    // Mở Zalo
    window.open("https://zalo.me/0994190627", "_blank");
  };

  // Xử lý bắt đầu drag
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragging(true);
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  }, []);

  // Xử lý drag
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        e.preventDefault();

        // Tìm chat section container để giới hạn di chuyển
        // Tìm chat container bằng cách query từ document
        let chatContainer = document.querySelector(".chat-section-container");

        // Nếu không tìm thấy, fallback về container hiện tại
        if (!chatContainer && containerRef.current) {
          chatContainer = containerRef.current;
        }

        if (chatContainer) {
          const chatRect = chatContainer.getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();

          if (containerRect) {
            // Tính toán vị trí mới relative với chat container
            const newX = e.clientX - chatRect.left - dragOffset.x;
            const newY = e.clientY - chatRect.top - dragOffset.y;

            // Giới hạn trong phạm vi chat section
            const maxX = chatRect.width - 32; // 32px = w-8
            const maxY = chatRect.height - 32;

            setPosition({
              x: Math.max(0, Math.min(newX, maxX)),
              y: Math.max(0, Math.min(newY, maxY)),
            });
          }
        } else {
          // Fallback: giới hạn trong khu vực mặc định
          const newX = e.clientX - dragOffset.x;
          const newY = e.clientY - dragOffset.y;

          setPosition({
            x: Math.max(0, Math.min(newX, 268)), // Giả sử chat section rộng 300px
            y: Math.max(0, Math.min(newY, 568)), // Giả sử chat section cao 600px
          });
        }
      }
    },
    [isDragging, dragOffset]
  );

  // Xử lý kết thúc drag
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Thêm event listeners cho drag
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove, {
        passive: false,
      });
      document.addEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "grabbing";
      document.body.style.userSelect = "none";

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        document.body.style.cursor = "";
        document.body.style.userSelect = "";
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Không render gì nếu chưa phải client
  if (!isClient) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="absolute z-40 select-none"
      suppressHydrationWarning
      style={{
        left: position.x,
        top: position.y,
        transform: "translate(0, 0)",
      }}
    >
      {/* Main Button */}
      <div className="w-8 h-8 cursor-move" onMouseDown={handleMouseDown}>
        <button
          onClick={toggleExpanded}
          className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:shadow-xl text-white rounded-full shadow-lg transition-all duration-300 ease-in-out transform hover:scale-110"
          aria-label="Liên hệ"
        >
          <svg
            className="w-5 h-5 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        </button>
      </div>

      {/* Telegram Button */}
      <button
        onClick={handleTelegram}
        className={`absolute bottom-0 right-0 w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg transition-all duration-300 ease-in-out transform hover:scale-110 ${
          isExpanded
            ? "translate-y-[-6rem] translate-x-0 opacity-100 scale-100"
            : "translate-y-0 translate-x-0 opacity-0 scale-75 pointer-events-none"
        } ${isDragging ? "pointer-events-none" : ""}`}
        aria-label="Telegram"
      >
        <img
          src="/icon/telegram.png"
          alt="Telegram"
          className="w-10 h-10 mx-auto"
        />
      </button>

      {/* Zalo Button */}
      <button
        onClick={handleZalo}
        className={`absolute bottom-0 right-0 w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 ease-in-out transform hover:scale-110 ${
          isExpanded
            ? "translate-y-[-2.8rem] translate-x-0 opacity-100 scale-100"
            : "translate-y-0 translate-x-0 opacity-0 scale-75 pointer-events-none"
        } ${isDragging ? "pointer-events-none" : ""}`}
        aria-label="Zalo"
      >
        <img
          src="/icon/zalo.png"
          alt="Zalo"
          className="w-10 h-10 mx-auto"
        />
      </button>
    </div>
  );
}
