"use client";

import React, { useEffect, useState, useCallback } from "react";

export default function ScrollControls() {
  const [showScrollUp, setShowScrollUp] = useState(false);
  const [showScrollDown, setShowScrollDown] = useState(false);
  const [isChatScreen, setIsChatScreen] = useState(false);

  const updateVisibility = useCallback(() => {
    // Detect chat screen by presence of chat container
    const chatContainer = document.querySelector(
      "[data-chat-container]"
    ) as HTMLElement | null;
    const chatScreenDetected = !!chatContainer;
    setIsChatScreen(chatScreenDetected);

    // If chat screen is present, hide global controls to avoid duplication
    if (chatScreenDetected) {
      setShowScrollUp(false);
      setShowScrollDown(false);
      return;
    }

    const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight || 0;
    const fullHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.offsetHeight,
      document.body.clientHeight,
      document.documentElement.clientHeight
    );

    const nearTopThreshold = 150; // px
    const nearBottomThreshold = 24; // px

    setShowScrollUp(scrollTop > nearTopThreshold);
    setShowScrollDown(fullHeight - (scrollTop + viewportHeight) > nearBottomThreshold);
  }, []);

  useEffect(() => {
    updateVisibility();
    const onScroll = () => updateVisibility();
    window.addEventListener("scroll", onScroll, { passive: true });
    window.addEventListener("resize", onScroll);

    // Observe DOM changes to detect when chat container mounts/unmounts
    const observer = new MutationObserver(() => {
      updateVisibility();
    });
    observer.observe(document.body, { childList: true, subtree: true });

    // Fallback: periodic check in case observer misses
    const interval = window.setInterval(updateVisibility, 1000);
    return () => {
      window.removeEventListener("scroll", onScroll);
      window.removeEventListener("resize", onScroll);
      observer.disconnect();
      window.clearInterval(interval);
    };
  }, [updateVisibility]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const scrollToBottom = () => {
    const fullHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight
    );
    window.scrollTo({ top: fullHeight, behavior: "smooth" });
  };

  // Do not render global scroll controls on chat screens
  if (isChatScreen) return null;

  return (
    <div className="pointer-events-none fixed right-4 bottom-20 z-50 flex flex-col items-end gap-3">
      {/* Scroll Up */}
      {showScrollUp && (
        <button
          aria-label="Cuộn lên đầu"
          onClick={scrollToTop}
          className="pointer-events-auto bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200"
        >
          <svg className="w-5 h-5 rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      )}

      {/* Scroll Down */}
      {/* {showScrollDown && (
        <button
          aria-label="Cuộn xuống cuối"
          onClick={scrollToBottom}
          className="pointer-events-auto bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      )} */}
    </div>
  );
}