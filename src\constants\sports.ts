import { FILTER_TYPES, type FilterType } from './filters';

export interface SportTab {
  label: string;
  icon: string;
  iconActive: string;
  category: string;
}

export interface FilterTab {
  label: string;
  icon: string;
  iconActive: string;
  filterType: FilterType;
}

export const SPORTS_TABS: SportTab[] = [
  { 
    label: "Bóng đá", 
    icon: "/icon/bong-da.svg", 
    iconActive: "/icon/bong-da-active.svg",
    category: "football"
  },
  { 
    label: "Bóng rổ", 
    icon: "/icon/bong-ro.svg", 
    iconActive: "/icon/bong-ro-active.svg",
    category: "basketball"
  },
  { 
    label: "Bóng chuyền", 
    icon: "/icon/bong-chuyen.svg", 
    iconActive: "/icon/bong-chuyen-active.svg",
    category: "volleyball"
  },
  { 
    label: "Talk show", 
    icon: "/icon/talk-show.svg", 
    iconActive: "/icon/talk-show-active.svg",
    category: "talkshow"
  },
  { 
    label: "Esports", 
    icon: "/icon/esport.svg", 
    iconActive: "/icon/esport-ative.svg",
    category: "esports"
  },
];

export const FILTER_TABS: FilterTab[] = [
  { 
    label: "Trận hot", 
    icon: "/icon/hot.svg", 
    iconActive: "/icon/hot-active.svg",
    filterType: FILTER_TYPES.HOT
  },
  { 
    label: "Trực tiếp", 
    icon: "/icon/live.svg", 
    iconActive: "/icon/live-active.svg",
    filterType: FILTER_TYPES.LIVE
  },
  { 
    label: "Hôm nay", 
    icon: "/icon/calendar.svg", 
    iconActive: "/icon/calendar-active.svg",
    filterType: FILTER_TYPES.TODAY
  },
  { 
    label: "Ngày mai", 
    icon: "/icon/calendar.svg", 
    iconActive: "/icon/calendar-active.svg",
    filterType: FILTER_TYPES.TOMORROW
  },
  { 
    label: "Tất cả", 
    icon: "", 
    iconActive: "",
    filterType: FILTER_TYPES.ALL
  },
];

export const BOTTOM_MENU_ITEMS = [
  {
    label: "Live",
    href: "/",
    icon: "/icon/live-active.svg",
    iconActive: "/icon/live.svg",
    iconSize: "w-5 h-5",
    hasActiveIcon: true
  },
  {
    label: "Tin Tức",
    href: "/tin-tuc",
    icon: "/icon/social.svg",
    iconSize: "w-6 h-6",
    hasActiveIcon: false
  },
  {
    label: "Chat",
    href: "/chat",
    icon: "/icon/chat.svg",
    iconActive: "/icon/chat-active.svg",
    iconSize: "w-6 h-6",
    hasActiveIcon: true
  },
  {
    label: "Khuyến Mãi",
    href: "/khuyen-mai",
    icon: "/icon/promo.svg",
    iconSize: "w-6 h-6",
    hasActiveIcon: false
  },
];
