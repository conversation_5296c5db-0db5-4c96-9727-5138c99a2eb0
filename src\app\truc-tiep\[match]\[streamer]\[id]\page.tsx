"use client";

import LiveMatchLayout from "@/components/features/live/LiveMatchLayout";
import { useDetection, useMobile } from "@/hooks";
import useLiveMatch from "@/hooks/useLiveMatch";

export default function LiveDetailRefactored() {
  const {
    // States
    isLoggedIn,
    videoUrl,
    matchData,
    blvInfo,
    loading,
    matchId,
    // streamerName,
    matchSlug,
    // Handlers
  } = useLiveMatch();

  const { isMobile } = useDetection();

  return (
    <LiveMatchLayout
      videoUrl={videoUrl}
      matchData={matchData}
      blvInfo={blvInfo}
      loading={loading}
      isLoggedIn={isLoggedIn}
      onOpenAuthModal={() => {}}
      matchId={matchId}
      matchSlug={matchSlug}
      isMobile={isMobile}
    />
  );
}
