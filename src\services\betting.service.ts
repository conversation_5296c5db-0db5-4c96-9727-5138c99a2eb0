import type { SupabaseClient } from "@supabase/supabase-js";
import { BettingOdds } from "@/types/betting.types";

export class BettingService {
  constructor(private supabase: SupabaseClient) {}

  async getWinningBettingOdds(): Promise<BettingOdds[] | null> {
    const { data, error } = await this.supabase
      .from("betting_odds")
      .select("*")
      .or("status.eq.win,status.is.null")
      .order("created_at", { ascending: false });
    if (error) {
      console.error("Error fetching betting odds:", error.message);
      return null;
    }
    return data;
  }

  async getLatestBettingOdds(limit: number = 5): Promise<BettingOdds[] | null> {
    const { data, error } = await this.supabase
      .from("betting_odds")
      .select("*")
      .or("status.eq.win,status.is.null")
      .order("created_at", { ascending: false })
      .limit(limit);
    if (error) {
      console.error("Error fetching latest betting odds:", error.message);
      return null;
    }
    return data;
  }
}
