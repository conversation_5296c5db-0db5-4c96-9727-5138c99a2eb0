"use client";

import { useEffect, useState } from "react";
import { Send, LogIn } from "lucide-react";

interface ChatFormProps {
  onLoginClick?: () => void;
  onSubmit?: (message: string, displayName: string) => void;
  noBorderRadius?: boolean;
}

export default function ChatForm({
  onLoginClick,
  onSubmit,
  noBorderRadius = false,
}: ChatFormProps) {
  const [message, setMessage] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [hiddenDisplayName, setHiddenDisplayName] = useState(false);

  // Đọc displayName từ localStorage khi mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem("chatDisplayName");
      if (saved) {
        setHiddenDisplayName(true);
        setDisplayName(saved);
      }
    } catch (err) {
      // Bỏ qua nếu môi trường không hỗ trợ hoặc lỗi truy cập
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    if (onSubmit) {
      onSubmit(message, displayName);
      setMessage("");
    } else {
      setMessage("");
    }

    // Lưu displayName vào localStorage sau khi submit
    try {
      if (displayName.trim()) {
        localStorage.setItem("chatDisplayName", displayName.trim());
        setHiddenDisplayName(true);
      }
    } catch (err) {
      // Bỏ qua nếu không thể lưu
    }
  };

  const handleLoginButtonClick = () => {
    if (onLoginClick) {
      onLoginClick();
    }
  };

  return (
    <div
      className={`border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-3 z-30 ${
        noBorderRadius ? "rounded-none" : "rounded-b-[10px]"
      }`}
    >
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Tên hiển thị */}
        {(!displayName || !hiddenDisplayName) && (
          <div className="relative">
          <input
            type="text"
            placeholder="Tên hiển thị"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 
                 bg-white dark:bg-custom-dark px-3 py-2 pr-10 text-sm 
                 text-gray-900 dark:text-white placeholder-gray-500 
                 dark:placeholder-gray-400 focus:outline-none focus:ring-2 
                 focus:ring-blue-500 dark:focus:ring-blue-600"
          />
            <button
              type="button"
              onClick={handleLoginButtonClick}
              className="absolute inset-y-0 right-2 flex items-center text-blue-600 dark:text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 transition-colors"
              title="Đăng nhập"
            >
              <LogIn size={18} />
            </button>
          </div>
        )}

        {/* Nội dung */}
        <div className="relative">
          <input
            type="text"
            placeholder="Nội dung"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full rounded-full border border-gray-300 dark:border-gray-600 
               bg-white dark:bg-custom-dark px-3 py-2 pr-12 text-sm 
               text-gray-900 dark:text-white placeholder-gray-500 
               dark:placeholder-gray-400 focus:outline-none focus:ring-2 
               focus:ring-blue-500 dark:focus:ring-blue-600"
          />
          <button
            type="submit"
            className="absolute top-1/2 right-0 -translate-y-1/2
             flex flex-row items-center justify-center
             rounded-full px-4 text-blue-500 hover:text-green-600
             transition-colors rotate-[45deg]"
            title="Gửi tin nhắn"
            disabled={!message.trim()} // disable nếu chưa nhập nội dung
          >
            <Send size={18} />
          </button>
        </div>
      </form>
    </div>
  );
}
