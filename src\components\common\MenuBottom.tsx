"use client";

import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { BOTTOM_MENU_ITEMS } from "@/constants/sports";

export default function MenuBottom() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-custom-dark border-t border-gray-200 dark:border-custom-dark-secondary z-50">
      <div className="flex items-center justify-around py-1">
        {BOTTOM_MENU_ITEMS.map((item) => {
          const active = isActive(item.href);
          const iconSrc =
            active && item.hasActiveIcon && item.iconActive
              ? item.iconActive
              : item.icon;
          return (
            <Link
              key={item.label}
              href={item.href}
              className="flex flex-col items-center gap-0.5 px-2 py-1 rounded-lg transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <div
                className={`${item.iconSize} flex items-center justify-center`}
              >
                <Image
                  src={iconSrc}
                  alt={item.label}
                  width={item.iconSize === "w-5 h-5" ? 20 : 24}
                  height={item.iconSize === "w-5 h-5" ? 20 : 24}
                  priority
                  className={`${item.iconSize} transition-all ${
                    active && !item.hasActiveIcon
                      ? "filter-blue"
                      : ""
                  }`}
                />
              </div>
              <span
                className={`text-xs font-medium transition-colors ${
                  active ? "text-blue-500" : "text-gray-700 dark:text-custom-muted"
                }`}
              >
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
