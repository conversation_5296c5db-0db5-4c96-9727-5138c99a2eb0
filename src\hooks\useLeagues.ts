import { useEffect, useState } from "react";
import {
  fetchLeagues,
  fetchLeagueDetails,
  fetchLeagueStandings,
  fetchLeagueFixtures,
  fetchSearchLeagues,
} from "@/services/leagueService";
import {
  League,
  LeagueGroup,
  LeagueResponse,
  LeagueDetails,
  MatchesResponse,
  MatchItem,
} from "@/types/league.types";
import { RankingResponse, Standing } from "@/types/standing.types";

export function useLeagues() {
  const [popularLeagues, setPopularLeagues] = useState<League[]>([]);
  const [countries, setCountries] = useState<LeagueGroup[]>([]);
  const [international, setInternational] = useState<LeagueGroup[]>([]);
  const [leagueDetails, setLeagueDetails] = useState<
    Record<string, LeagueDetails>
  >({});
  const [selectedLeagueId, setSelectedLeagueId] = useState<string>("");

  useEffect(() => {
    fetchLeagues()
      .then((res: LeagueResponse) => {
        setPopularLeagues(res.data.popular);
        setCountries(res.data.countries);
        setInternational(res.data.international);
      })
      .catch((err) => {
        console.error("Fetch leagues failed:", err);
        setPopularLeagues([]);
        setCountries([]);
        setInternational([]);
      });
  }, []);

  useEffect(() => {
    if (!selectedLeagueId || leagueDetails[selectedLeagueId]) return;
    fetchLeagueDetails(selectedLeagueId)
      .then((data) => {
        setLeagueDetails((prev) => ({ ...prev, [selectedLeagueId]: data }));
      })
      .catch((err) => console.error("Fetch league detail failed:", err));
  }, [selectedLeagueId, leagueDetails]);

  return {
    popularLeagues,
    countries,
    international,
    leagueDetails,
    selectedLeagueId,
    setSelectedLeagueId,
  };
}

export function useLeagueStanding(league_id: string) {
  const [standings, setStandings] = useState<Standing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!league_id) {
      setStandings([]);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    fetchLeagueStandings(league_id)
      .then((res: RankingResponse[]) => {
        // lấy phần tử đầu tiên trong mảng
        const first = res[0];
        const table =
          first?.data.table ?? first?.data.tables?.[0]?.table;

        if (table) {
          setStandings(table.all);
        } else {
          setStandings([]);
        }
      })
      .catch((err) => {
        console.error("Fetch standings error:", err);
        setError("Lỗi tải BXH");
        setStandings([]);
      })
      .finally(() => setLoading(false));
  }, [league_id]);

  return { standings, loading, error };
}

export function useLeagueMatch(league_id: string) {
  const [matches, setMatches] = useState<MatchItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!league_id) return;
    setLoading(true);

    fetchLeagueFixtures(undefined, league_id)
      .then((res: MatchesResponse) => {
        // API trả về { matches: Record<string, MatchItem[]> }
        const flat: MatchItem[] = Object.values(res.matches).flat();
        setMatches(flat);
      })
      .catch((err) => {
        console.error("Fetch matches error:", err);
        setError("Lỗi tải trận đấu");
        setMatches([]);
      })
      .finally(() => setLoading(false));
  }, [league_id]);

  return { matches, loading, error };
}

export function useLeagueSearch(query: string) {
  const [results, setResults] = useState<League[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    let cancelled = false;
    setLoading(true);

    fetchSearchLeagues(query)
      .then((data) => {
        if (!cancelled) setResults(data);
      })
      .catch(() => {
        if (!cancelled) setResults([]);
      })
      .finally(() => {
        if (!cancelled) setLoading(false);
      });

    return () => {
      cancelled = true;
    };
  }, [query]);

  return { results, loading };
}

