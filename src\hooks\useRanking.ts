import { RankingTable } from "@/types/standing.types";
import { useEffect, useState } from "react";
import { fetchLeagueStandings } from "../services/leagueService";

export default function useRanking(leagueId: string) {
  const [loadingTable, setLoadingTable] = useState(true);
  const [rankingTable, setRankingTable] = useState<RankingTable["table"]>({
    all: [],
    away: [],
    home: [],
    xg: [],
  });

  useEffect(() => {
    fetchLeagueStandings(leagueId).then((response) => {
      setLoadingTable(false);
      const data = response?.[0]?.data ?? null;
      if (data) {
        if (data?.table) {
          setRankingTable(data?.table);
        } else if (data?.tables?.[0]?.table) {
          setRankingTable(data?.tables?.[0]?.table);
        }
      }
    });
  }, [leagueId]);

  return { rankingTable, loadingTable };
}
