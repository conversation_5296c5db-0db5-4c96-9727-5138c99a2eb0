import {
  League,
  LeagueResponse,
  LeagueDetails,
  MatchesResponse,
  LeagueResponseSearch,
} from "@/types/league.types";
import { RankingResponse } from "@/types/standing.types";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.ngoaihangtv.live";

// ✅ Danh sách league
export async function fetchLeagues(
  status?: string,
  limit = 1000,
  offset = 0
): Promise<LeagueResponse> {
  const url = new URL(`${API_BASE_URL}/leagues`);
  if (status) url.searchParams.set("status", status);
  url.searchParams.set("limit", String(limit));
  url.searchParams.set("offset", String(offset));

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error("Failed to fetch leagues");
  return res.json() as Promise<LeagueResponse>;
}

// ✅ Search league
export async function fetchSearchLeagues(query: string): Promise<League[]> {
  const res = await fetch(
    `${API_BASE_URL}/leagues/search?query=${encodeURIComponent(query)}`
  );
  if (!res.ok) throw new Error("Failed to fetch leagues");

  const json: LeagueResponseSearch = await res.json();

  const popular = json.popular ?? [];
  const international = json.international?.leagues ?? []; // 👈 lấy leagues chứ không phải chính nó
  const countries = (json.countries ?? []).flatMap((c) => c.leagues ?? []);

  const allLeagues: League[] = [
    ...popular,
    ...international,
    ...countries,
  ];

  return allLeagues;
}

// ✅ Chi tiết 1 league
export async function fetchLeagueDetails(
  league_id: string
): Promise<LeagueDetails> {
  const res = await fetch(`${API_BASE_URL}/leagues/${league_id}`);
  if (!res.ok) throw new Error("Failed to fetch league details");
  return res.json() as Promise<LeagueDetails>;
}

// ✅ BXH
export async function fetchLeagueStandings(league_id: string) {
  try {
    const res = await fetch(`${API_BASE_URL}/standings/${league_id}`);
    if (!res.ok) throw new Error("Failed to fetch standings");
    return (await res.json()) as RankingResponse[];
  } catch (error) {
    console.error("Error fetching league standings:", error);
    throw error;
  }
}

// ✅ Fixtures / Matches
export async function fetchLeagueFixtures(
  date?: string,
  league_id?: string,
  limit?: number,
  offset?: number
): Promise<MatchesResponse> {
  const url = new URL(`${API_BASE_URL}/fixtures`);
  if (date) url.searchParams.set("date", date);
  if (league_id) url.searchParams.set("leagueId", league_id);
  if (limit !== undefined) url.searchParams.set("limit", String(limit));
  if (offset !== undefined) url.searchParams.set("offset", String(offset));

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error("Failed to fetch fixtures");
  return res.json() as Promise<MatchesResponse>;
}
