"use client";

import useRanking from "@/hooks/useRanking";
import { StandingTeam } from "@/types/league.types";
import Link from "next/link";
import { useParams } from "next/navigation";

export default function StandingsPage() {
  const params = useParams();
  const leagueIdFromUrl = (params?.leagueId as string | undefined) || "";

  const { rankingTable, loadingTable } = useRanking(leagueIdFromUrl);

  return (
    <div className="flex flex-col md:flex-row gap-6">
      <main className="flex-1">
        {/* Tabs */}
        {leagueIdFromUrl && (
          <div className="flex gap-2 mb-4 rounded-2xl p-2">
            <Link
              href={`/bxh-va-lich-thi-dau/${leagueIdFromUrl}/lich-thi-dau`}
              className="flex-1 rounded-xl px-4 py-3 text-center text-base font-semibold transition-colors 
                         bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              style={{ minWidth: 180 }}
            >
              Trận
            </Link>
            <Link
              href={`/bxh-va-lich-thi-dau/${leagueIdFromUrl}/bxh`}
              className="flex-1 rounded-xl px-4 py-3 text-center text-base font-semibold transition-colors 
                         bg-gradient-to-r from-blue-600 to-blue-400 dark:from-blue-700 dark:to-blue-500 text-white"
              style={{ minWidth: 180 }}
            >
              Bảng xếp hạng
            </Link>
          </div>
        )}

        {/* Standings Table */}
        <section className="bg-white dark:bg-gray-900 shadow rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
            <span className="border-l-4 border-blue-500 pl-2 mr-2">
              Bảng xếp hạng
            </span>
          </h2>

          <div className="overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-sm">
            <div className="relative overflow-x-auto">
              <div className="overflow-x-auto">
                <table className="w-full text-left text-sm table-fixed">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                      <th className="px-3 py-2 w-8">#</th>
                      <th className="px-3 py-2 w-32">Đội</th>
                      <th className="px-3 py-2 w-12 text-center">Tr</th>
                      <th className="px-3 py-2 w-12 text-center">Th</th>
                      <th className="px-3 py-2 w-12 text-center">H</th>
                      <th className="px-3 py-2 w-12 text-center">B</th>
                      <th className="px-3 py-2 w-16 text-center">Điểm</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loadingTable && (
                      <tr>
                        <td
                          colSpan={7}
                          className="text-center py-4 text-gray-500 dark:text-gray-400"
                        >
                          Đang tải...
                        </td>
                      </tr>
                    )}

                    {!loadingTable && rankingTable.all.length === 0 && (
                      <tr>
                        <td
                          colSpan={7}
                          className="text-center py-4 text-gray-500 dark:text-gray-400"
                        >
                          Không có dữ liệu
                        </td>
                      </tr>
                    )}
                    {!loadingTable &&
                      rankingTable.all.map((r: StandingTeam, idx: number) => (
                        <tr
                          key={`standing-${r.id}-${idx}`}
                          className="border-b border-gray-100 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-blue-900 transition-colors"
                        >
                          <td className="px-3 py-2 w-8 text-gray-600 dark:text-gray-400">
                            {r.idx ?? idx + 1}
                          </td>
                          <td className="px-3 py-2 w-32 font-medium text-gray-900 dark:text-white">
                            <div className="flex items-center gap-2 min-w-0">
                              <img
                                src={`https://images.fotmob.com/image_resources/logo/teamlogo/${r.id}_xsmall.png`}
                                alt={r.name || r.shortName || ""}
                                className="w-6 h-6 object-contain rounded-full shrink-0"
                              />
                              <span
                                className="text-sm md:text-base truncate"
                                title={r.name || r.shortName || ""}
                              >
                                {r.name || r.shortName || ""}
                              </span>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-center">
                            {r.played ?? ""}
                          </td>
                          <td className="px-3 py-2 text-center">
                            {r.wins ?? ""}
                          </td>
                          <td className="px-3 py-2 text-center">
                            {r.draws ?? ""}
                          </td>
                          <td className="px-3 py-2 text-center">
                            {r.losses ?? ""}
                          </td>
                          <td className="px-3 py-2 text-center font-semibold text-gray-900 dark:text-white">
                            {r.pts ?? ""}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
