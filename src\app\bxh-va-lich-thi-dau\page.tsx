"use client";

import { useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useLeagues } from "@/hooks/useLeagues";

export default function BxhVaLichThiDauPage() {
  const router = useRouter();
  const params = useParams();
  const { selectedLeagueId } = useLeagues();

  // Lấy id từ URL nếu có
  const leagueIdFromUrl = params?.leagueId as string | undefined;
  const effectiveLeagueId = leagueIdFromUrl ?? selectedLeagueId;

  useEffect(() => {
    if (effectiveLeagueId) {
      router.replace(`/bxh-va-lich-thi-dau/${effectiveLeagueId}/lich-thi-dau`);
    }
  }, [effectiveLeagueId, router]);

  return null; // Không render gì ở đây
}