"use client";

import React, { createContext, useContext, useEffect, useRef } from "react";
import { useAuthService } from "@/hooks/useAuthService";
import { useChatService } from "@/hooks/useChatService";
import type { User } from "@supabase/supabase-js";
import type { AuthProfile, LoginCredentials, RegisterData } from "@/types";
import type { ChatRoom, ChatMessage } from "@/types";

interface AuthContextType {
  user: User | null;
  profile: AuthProfile | null;
  loading: boolean;
  chatRooms: ChatRoom[];
  chatRoomsLoading: boolean;
  messages: ChatMessage[];
  messagesLoading: boolean;
  signIn: (
    credentials: LoginCredentials
  ) => Promise<{ user: User | null; error: Error | null }>;
  signUp: (
    registerData: RegisterData
  ) => Promise<{ user: User | null; error: Error | null }>;
  signInWithGoogle: () => Promise<{ error: Error | null }>;
  signOut: () => Promise<{ error: Error | null }>;
  updateProfile: (
    userId: string,
    updates: Partial<AuthProfile>
  ) => Promise<{ profile: AuthProfile | null; error: Error | null }>;
  loadChatRooms: () => Promise<void>;
  reloadChatRooms: () => Promise<void>;
  loadMessages: (roomId: string) => Promise<void>;
  loadMessagesForRoom: (roomIdentifier: string) => Promise<void>;
  sendMessage: (
    roomId: string,
    content: string
  ) => Promise<{ error: Error | null }>;
  sendAnonymousMessage: (
    roomId: string,
    content: string,
    senderDisplayName?: string
  ) => Promise<{ error: Error | null }>;
  createChat: (
    name: string,
    type: "direct" | "group" | "private"
  ) => Promise<
    | {
        error: Error;
        room?: undefined;
      }
    | {
        room: ChatRoom | null;
        error: null;
      }
  >;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const authService = useAuthService();
  const chatService = useChatService(authService.user?.id || "");
  const loadChatRoomsRef = useRef(chatService.loadChatRooms);

  // Update ref when loadChatRooms changes
  useEffect(() => {
    loadChatRoomsRef.current = chatService.loadChatRooms;
  }, [chatService.loadChatRooms]);

  // Load chat rooms when user logs in
  useEffect(() => {
    if (authService.user?.id && !authService.loading) {
      loadChatRoomsRef.current();
    } else {
    }
  }, [authService.user?.id, authService.loading]);

  const contextValue: AuthContextType = {
    ...authService,
    chatRooms: chatService.chatRooms,
    chatRoomsLoading: chatService.loading,
    messages: chatService.messages,
    messagesLoading: chatService.messagesLoading,
    loadChatRooms: chatService.loadChatRooms,
    reloadChatRooms: chatService.reloadChatRooms,
    loadMessages: chatService.loadMessages,
    loadMessagesForRoom: chatService.loadMessagesForRoom,
    sendMessage: chatService.sendMessage,
    createChat: chatService.createChat,
    sendAnonymousMessage: chatService.sendAnonymousMessage,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
