"use client";

import AuthModal from "@/components/AuthModal";
import { useAuth } from "@/contexts/AuthContext";
import { useBanner } from "@/hooks/useBanner";
import useBettingOdds from "@/hooks/useBettingOdds";
import { ChatRoom } from "@/types";
import { useMemo } from "react";

interface BettingOddsTabProps {
  onCreateChat: (
    name: string,
    type: "direct" | "group" | "private"
  ) => Promise<
    | {
      error: Error;
      room?: undefined;
    }
    | {
      room: ChatRoom | null;
      error: null;
    }
  >;
  onOddsClick?: (roomId: string) => void;
  canAccessBetting?: boolean;
  profileLoading?: boolean;
}

export default function BettingOddsTab({
  canAccessBetting = false,
  profileLoading = false,
}: BettingOddsTabProps) {
  const { bettingOdds, loading, winningBettingOdds } = useBettingOdds();
  const { user } = useAuth();
  const {
    openAuthModal,
    closeAuthModal,
    isAuthModalOpen,
    authMode,
    setAuthMode,
  } = useBanner();

  const odds = useMemo(() => {
    if (!user && winningBettingOdds) {
      return winningBettingOdds;
    }

    if (user && !canAccessBetting) {
      return bettingOdds.filter(odds => odds.status === "win");
    }

    return bettingOdds;
  }, [user, bettingOdds, winningBettingOdds, canAccessBetting]);

  // const onBannerClick = async (odds: BettingOdds) => {
  //   if (!user) {
  //     // User not logged in, show auth modal
  //     openAuthModal?.("login");
  //     return;
  //   }

  //   if (!odds) return;

  //   try {
  //     // Create 1-1 chat with admin
  //     const chatName = `Chat với ${odds.expert.name}`;
  //     const chatType = "direct";

  //     const { room, error } = await onCreateChat?.(chatName, chatType);

  //     if (error) {
  //       console.error("Error creating chat:", error);
  //       return;
  //     }

  //     if (room) {
  //       onOddsClick?.(room?.id);
  //     }
  //   } catch (error) {
  //     console.error("Error handling banner click:", error);
  //   }
  // };

  if (loading || profileLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!odds || odds.length === 0) {
    return (
      <div className="p-4 text-center">
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6">
          <div className="w-12 h-12 mx-auto mb-3 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
          </div>
          <h3 className="text-base font-semibold text-gray-700 dark:text-gray-300 mb-1">
            Chưa có kèo nào
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Hiện tại chưa có thông tin kèo cược
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto custom-scrollbar">
      <div className="space-y-4 p-2">
        {odds.map((odds) => {
          return (
            <div
              key={odds.id}
              className={`transition-all duration-700 ease-out cursor-pointer opacity-100 translate-y-0`}
            // onClick={() => onBannerClick?.(odds)}
            >
              <div className="relative overflow-hidden">
                <img
                  src="/banner-chat/5.png"
                  alt="Banner Background"
                  className="w-full h-20 object-cover"
                />

                <img
                  src="/banner-chat/6.png"
                  alt="Banner Secondary"
                  className="w-full h-7 object-cover"
                />

                <div className="absolute bottom-0 left-0 right-0 h-7 flex items-center">
                  <div className="flex-[0.6] h-full flex items-center px-3 gap-2">
                    <img
                      src="/banner-chat/3.png"
                      alt="Chat Icon"
                      className="w-5 h-5 object-contain ml-1"
                    />
                    <span className="text-white text-[13px] font-medium">
                      {odds.saying || "Tin nhắn"}
                    </span>
                  </div>

                  <div className="flex-[0.4] bg-red-800 h-7 flex items-center px-3 gap-2">
                    <img
                      src="/banner-chat/4.png"
                      alt="Hand Icon"
                      className="w-4 h-4 object-contain"
                    />
                    <span className="text-white text-[11px] font-bold uppercase text-center">
                      {odds.type || "KÈO VIP"}
                    </span>
                  </div>
                </div>

                <div className="absolute top-0 left-0 right-0 h-20 bg-black/30 flex items-center px-1">
                  <div
                    className="flex-3 flex items-center pl-0"
                    style={{ width: "40%" }}
                  >
                    <div className="relative" style={{ marginLeft: "10px" }}>
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center overflow-hidden">
                        {odds.expert.avatar_url ? (
                          <img
                            src={odds.expert.avatar_url}
                            alt="Expert Avatar"
                            className="w-full h-full object-cover rounded-full"
                          />
                        ) : (
                          <img
                            src="/banner-chat/1.png"
                            alt="Default Avatar"
                            className="w-full h-full object-cover rounded-full"
                          />
                        )}
                      </div>
                      <div className="absolute -bottom-0 -right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                    </div>

                    <div className="flex flex-col ml-1 min-w-0">
                      <span className="text-white font-bold text-xs drop-shadow-lg truncate">
                        {odds.expert.name || "AD Long Thiên"}
                      </span>
                      <span className="text-white/80 text-xs drop-shadow-lg truncate">
                        Đang hoạt động
                      </span>
                    </div>
                  </div>

                  <div
                    className="flex-4 flex flex-col items-center text-left px-1 relative"
                    style={{ width: "45%" }}
                  >
                    <div
                      className={`text-white/90 text-xs font-medium drop-shadow-lg mb-1 transition-opacity duration-300 ${
                        odds.status?.trim() === "win"
                          ? "opacity-30"
                          : "opacity-90"
                        }`}
                    >
                      {odds.tournament || "BÓNG ĐÁ - GIẢI LIGUE 1 PHÁP [FT]"}
                    </div>
                    <div
                      className={`text-yellow-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${
                        odds.status?.trim() === "win"
                          ? "opacity-30"
                          : "opacity-100"
                        }`}
                    >
                      {odds.team_name || "LORIENT (H) vs STADE RENNAIS"}
                    </div>
                    {odds.status?.trim() === "win" && (
                      <img
                        src="/banner-chat/2.png"
                        alt="Kèo Thắng"
                        className="absolute top-1/2 left-1/2 w-[120px] h-[60px] -translate-x-1/2 -translate-y-1/2 object-contain opacity-90 transform -rotate-12"
                      />
                    )}
                  </div>

                  <div
                    className="flex-3 flex flex-col items-center justify-center"
                    style={{ width: "20%" }}
                  >
                    <span
                      className={`text-green-400 font-bold text-lg drop-shadow-lg transition-opacity duration-300 ${
                        odds.status?.trim() === "win"
                          ? "opacity-30"
                          : "opacity-100"
                        }`}
                    >
                      {odds.closing_bet?.split(" ")[0] || ""}
                    </span>
                    <span
                      className={`text-green-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${
                        odds.status?.trim() === "win"
                          ? "opacity-30"
                          : "opacity-100"
                        }`}
                    >
                      {odds.closing_bet?.split(" ").slice(1).join(" ") || ""}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Thông báo liên hệ admin cho user không có quyền */}
        {user && !canAccessBetting && (
          <div className="mt-6 p-6">
            <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-8 max-w-md mx-auto text-center border border-orange-200 dark:border-orange-800">
              <div className="w-16 h-16 mx-auto mb-4 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-orange-600 dark:text-orange-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <p className="text-orange-700 dark:text-orange-300 mb-4">
                Vui lòng liên hệ admin để soi kèo cùng chuyên gia
              </p>
              <div className="flex flex-col gap-3">
                <a
                  href="https://t.me/thuphuongepl"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                >
                  <img
                    src="/icon/telegram.png"
                    alt="Telegram"
                    className="w-5 h-5"
                  />
                  Liên hệ qua Telegram
                </a>
                <a
                  href="https://zalo.me/0994190627"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                >
                  <img
                    src="/icon/zalo.png"
                    alt="Zalo"
                    className="w-5 h-5"
                  />
                  Liên hệ qua Zalo
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Thông báo liên hệ chăm sóc khách hàng */}
      {user ? null : (
        <div className="sticky bottom-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-4 mx-2 mb-2 rounded-xl shadow-2xl border border-white/20 backdrop-blur-sm">
          <div className="flex flex-col items-center gap-3">
            {/* Tiêu đề với hiệu ứng mượt mà */}
            <div className="flex items-center justify-center gap-3 relative">
              <div
                className="absolute -inset-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg blur opacity-50 animate-pulse"
                style={{ animationDuration: "3s" }}
              ></div>
              <div className="relative flex items-center gap-3 bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-2 rounded-lg shadow-lg">
                <div className="w-3 h-3 bg-white rounded-full opacity-90"></div>
                <span className="text-black text-sm font-bold text-center">
                  🔥 LIÊN HỆ NGAY ĐỂ NHẬN KÈO VIP 🔥
                </span>
                <div className="w-3 h-3 bg-white rounded-full opacity-90"></div>
              </div>
            </div>

            {/* Thông tin liên hệ với hiệu ứng hover mượt */}
            <div className="flex items-center gap-6">
              <a
                href="https://t.me/thuphuongepl"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-400 hover:to-blue-500 px-4 py-2 rounded-full transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-blue-500/30 border-2 border-white/30 hover:border-white/50"
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur opacity-0 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative flex items-center gap-2">
                  <img
                    src="/icon/telegram.png"
                    alt="Telegram"
                    className="w-5 h-5 transition-transform duration-300 group-hover:scale-110"
                  />
                  <span className="text-white text-sm font-bold">TELEGRAM</span>
                </div>
              </a>

              <a
                href="https://zalo.me/0994190627"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 px-4 py-2 rounded-full transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-green-500/30 border-2 border-white/30 hover:border-white/50"
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur opacity-0 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative flex items-center gap-2">
                  <img
                    src="/icon/zalo.png"
                    alt="Zalo"
                    className="w-5 h-5 transition-transform duration-300 group-hover:scale-110"
                  />
                  <span className="text-white text-sm font-bold">ZALO</span>
                </div>
              </a>
            </div>

            {/* Dòng chữ phụ với hiệu ứng fade */}
            <div className="text-center">
              <span className="text-yellow-200 text-xs font-medium opacity-90">
                ⚡ Phản hồi nhanh 24/7 - Kèo chuẩn 100% ⚡
              </span>
            </div>
          </div>
        </div>
      )}

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        initialMode={authMode}
        onModeChange={setAuthMode}
        onLoginSuccess={() => {}}
      />
    </div>
  );
}
